export const INITIAL_CART_STATE={
    showCart:false,
    showWishlist:false,
}

export const ACTIONS_CART={
    TOGGLE_CART:'TOGGLE_CART',
    TOGGLE_WISHLIST:'TOGGLE_WISHLIST',
    ADD_TO_CART:'ADD_TO_CART'
}

export const reducerCartContext=(state,action)=>{
    switch (action.type) {
        case 'TOGGLE_CART':
            return{
                ...state,
                showCart:!state?.showCart,
                // showWishlist:false
            }
        case 'TOGGLE_WISHLIST':
            return{
                ...state,
                // showCart:false,
                showWishlist:!state?.showWishlist
            }
        case 'ADD_TO_CART':
            return{
                ...state,
                // showCart:false,
            }
        default:
            state
            break;
    }
}