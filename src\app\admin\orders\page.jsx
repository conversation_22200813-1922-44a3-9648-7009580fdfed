import { requireAdmin } from '@/middleware/adminAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import OrderManagement from '@/components/admin/OrderManagement';
import { Order } from '@/libs/mongoDb/Models/Order';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getOrdersData(searchParams) {
  await connectToDbAmieShop();
  
  const page = parseInt(searchParams.page) || 1;
  const limit = parseInt(searchParams.limit) || 20;
  const search = searchParams.search || '';
  const status = searchParams.status || '';
  const paymentStatus = searchParams.paymentStatus || '';
  const dateFrom = searchParams.dateFrom || '';
  const dateTo = searchParams.dateTo || '';

  // Build query
  let query = {};
  
  if (search) {
    query.$or = [
      { orderNumber: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } },
      { 'shippingAddress.firstName': { $regex: search, $options: 'i' } },
      { 'shippingAddress.lastName': { $regex: search, $options: 'i' } }
    ];
  }
  
  if (status) query.status = status;
  if (paymentStatus) query.paymentStatus = paymentStatus;
  
  if (dateFrom || dateTo) {
    query.createdAt = {};
    if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
    if (dateTo) query.createdAt.$lte = new Date(dateTo + 'T23:59:59.999Z');
  }

  const skip = (page - 1) * limit;

  const [orders, total] = await Promise.all([
    Order.find(query)
      .populate('userId', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Order.countDocuments(query)
  ]);

  // Get order statistics
  const stats = await Order.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalValue: { $sum: '$total' }
      }
    }
  ]);

  return {
    orders: JSON.parse(JSON.stringify(orders)),
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    },
    stats: JSON.parse(JSON.stringify(stats))
  };
}

export const metadata = {
  title: 'Order Management - Admin',
  description: 'Manage customer orders',
};

export default async function AdminOrdersPage({ searchParams }) {
  await requireAdmin();
  const data = await getOrdersData(searchParams);

  return (
    <AdminLayout>
      <OrderManagement {...data} />
    </AdminLayout>
  );
}
