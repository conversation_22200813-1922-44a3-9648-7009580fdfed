{"name": "l<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.0.0", "@react-three/fiber": "^9.0.4", "@types/three": "^0.173.0", "bcrypt": "^5.1.1", "build": "^0.1.4", "firebase": "^11.3.1", "mongoose": "^8.10.1", "next": "15.1.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.1", "three": "^0.173.0"}, "overrides": {"react": "$react", "react-dom": "$react-dom"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1"}}