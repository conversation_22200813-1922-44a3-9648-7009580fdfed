{"name": "l<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^3.9.1", "@headlessui/react": "^2.2.4", "@react-three/drei": "^10.0.0", "@react-three/fiber": "^9.0.4", "@stripe/stripe-js": "^7.3.1", "@types/three": "^0.173.0", "bcrypt": "^5.1.1", "build": "^0.1.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^11.3.1", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "mongoose": "^8.10.1", "next": "15.1.7", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "recharts": "^2.15.1", "stripe": "^18.1.1", "three": "^0.173.0", "zustand": "^5.0.5"}, "overrides": {"react": "$react", "react-dom": "$react-dom"}, "devDependencies": {"@eslint/eslintrc": "^3", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1"}}