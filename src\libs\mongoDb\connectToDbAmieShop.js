import mongoose from "mongoose"
export const connectToDbAmieShop = async () => {
    try {
        let db={}
        if(db.readyState==1){
            console.log('amieshop mongoDb already connected')
        }else{
            db=mongoose.connection.on('amieshop connected', () => console.log('connected to amieshop mongoDb'))
            await mongoose.connect(process.env.MONGO_DB1)
        }
    } catch (error) {
        mongoose.connection.on('error',err=>{logError(err)})
        handleError(error);
        mongoose.connection.close()
    }
}
