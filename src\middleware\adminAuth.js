import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { redirect } from 'next/navigation';

export async function requireAdmin() {
  const session = await getServerSession(authOptions);
  
  if (!session?.user) {
    redirect('/auth/signin?callbackUrl=/admin');
  }
  
  if (!session.user.isAdmin && session.user.role !== 'admin') {
    redirect('/unauthorized');
  }
  
  return session;
}

export function withAdminAuth(handler) {
  return async (request, context) => {
    try {
      const session = await requireAdmin();
      return handler(request, context, session);
    } catch (error) {
      return new Response('Unauthorized', { status: 401 });
    }
  };
}
