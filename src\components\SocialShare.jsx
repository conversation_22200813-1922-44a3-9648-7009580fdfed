'use client';

import { FacebookI<PERSON>, TwitterIcon, LinkedinIcon, LinkIcon } from 'lucide-react';
import { useState } from 'react';

export default function SocialShare({ url, title, description }) {
  const [copied, setCopied] = useState(false);

  const shareLinks = {
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const openShareWindow = (shareUrl) => {
    window.open(
      shareUrl,
      'share',
      'width=600,height=400,scrollbars=yes,resizable=yes'
    );
  };

  return (
    <div className="bg-gray-50 p-4 rounded-lg">
      <h4 className="text-sm font-medium text-gray-900 mb-3">Share this product</h4>
      <div className="flex items-center space-x-3">
        <button
          onClick={() => openShareWindow(shareLinks.facebook)}
          className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-colors"
          title="Share on Facebook"
        >
          <FacebookIcon className="w-5 h-5" />
        </button>
        
        <button
          onClick={() => openShareWindow(shareLinks.twitter)}
          className="flex items-center justify-center w-10 h-10 bg-blue-400 text-white rounded-full hover:bg-blue-500 transition-colors"
          title="Share on Twitter"
        >
          <TwitterIcon className="w-5 h-5" />
        </button>
        
        <button
          onClick={() => openShareWindow(shareLinks.linkedin)}
          className="flex items-center justify-center w-10 h-10 bg-blue-700 text-white rounded-full hover:bg-blue-800 transition-colors"
          title="Share on LinkedIn"
        >
          <LinkedinIcon className="w-5 h-5" />
        </button>
        
        <button
          onClick={copyToClipboard}
          className="flex items-center justify-center w-10 h-10 bg-gray-600 text-white rounded-full hover:bg-gray-700 transition-colors"
          title="Copy link"
        >
          <LinkIcon className="w-5 h-5" />
        </button>
        
        {copied && (
          <span className="text-sm text-green-600 font-medium">Copied!</span>
        )}
      </div>
    </div>
  );
}
