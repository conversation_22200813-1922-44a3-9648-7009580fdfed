'use client'
import Image from 'next/image'
import React, { useEffect, useRef } from 'react'

export default function ImageContainer({data,brightness,alt,className,style}) {
  // console.log('ImageContainer :-',data)
  return (
    <div className={`flex w-full h-full relative select-none flex-none ${brightness,style}`}>
      {data && <Image 
        src={data} 
        alt={alt ? alt : "hero images" }
        fill 
        className={`transition-opacity opacity-0 ${className} object-cover duration-[2s]`} 
        onLoadingComplete={(img)=>img.classList.remove('opacity-0')}
      />}
      {/* <div className='flex absolute top-0 left-0 w-full h-full bg-black/15'/> */}
    </div>
  )
}