'use client'
import { settings } from '@/libs/siteEcomSettimngs'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React from 'react'
import DashboardDeleteBtn from './DashboardDeleteBtn'

export default function DashboardProductSummary({data}) {
  const router=useRouter()
  const deleteProduct = async () => {
    try {
      const res=await fetch(`${settings.url}/api/products/${data?._id}`,{method:'DELETE'})
      data?.images.forEach(element => {
        // console.log(element)
        deleteFiles(element.path)
      });
      // if(!res.ok)return 
    } catch (error) {
      console.log(error)
    }
    router.refresh()
  }
  console.log('DashboardProductSummary:-',data)
  return (
    <div className='flex w-full text-sm gap-2 rounded h-16 border-[1px] border-gray-300'>
      <Link href={`/housemodels/dashboard/products/${data?._id}`} className='flex w-full gap-2 rounded h-16'>
        <div className='flex relative h-full p-2 flex-[1] items-center justify-center rounded'>
          <Image className='object-contain' src={data?.renders ? data?.renders[0]?.url : null} alt='product image' fill/>
        </div>
        <div className='hidden md:flex flex-[1] items-center justify-start truncate'>{data?._id}</div>
        <div className='flex flex-[1] items-center justify-center'>{data?.buildingTitle}</div>
        <div className='hidden md:flex flex-[1] items-center justify-center'>{data?.buildingtype}</div>
        <div className='flex flex-[1] items-center justify-center'>{data?.price}</div>
        <div className='flex flex-[1] items-center justify-center text-center'>{new Date(data?.createdAt).toDateString().slice(4)}</div>
      </Link>
      <div onClick={deleteProduct} className='flex cursor-pointer flex-[.5] h-full items-center justify-center mr-4'>
        <DashboardDeleteBtn data={data}/>
      </div>
    </div>
  )
}
