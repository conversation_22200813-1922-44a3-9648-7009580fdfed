'use client';

import Link from 'next/link';

const categories = [
  {
    id: 1,
    title: "A serum for everyone sounds about right!",
    subtitle: "Shop our collection of serums for all skin types.",
    buttonText: "Serums",
    buttonLink: "/collections/serums",
    image: "/images/serums-category.jpg",
    bgColor: "bg-gradient-to-br from-purple-100 to-pink-100"
  },
  {
    id: 2,
    title: "You scream, I scream and we all scream for eye cream!",
    subtitle: "Shop the Silki eye creams which recently launched.",
    buttonText: "Eye creams",
    buttonLink: "/collections/eye-cream",
    image: "/images/eye-cream-category.jpg",
    bgColor: "bg-gradient-to-br from-blue-100 to-cyan-100"
  },
  {
    id: 3,
    title: "Body care with actives is a must for the spring time!",
    subtitle: "Shop our range of body butters, creams and balms including the Standard Beauty Faded Body butter.",
    buttonText: "Body butters & creams",
    buttonLink: "/collections/body-care",
    image: "/images/body-care-category.jpg",
    bgColor: "bg-gradient-to-br from-green-100 to-emerald-100"
  }
];

export default function ProductCategories() {
  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category) => (
            <div
              key={category.id}
              className={`relative rounded-2xl overflow-hidden ${category.bgColor} group hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2`}
            >
              {/* Background Image */}
              <div className="absolute inset-0 opacity-20">
                <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                  <span className="text-gray-400">Category Image</span>
                </div>
              </div>

              {/* Content */}
              <div className="relative p-8 h-80 flex flex-col justify-between">
                <div className="space-y-4">
                  <h3 className="text-2xl font-bold text-gray-900 leading-tight">
                    {category.title}
                  </h3>
                  <p className="text-gray-700 leading-relaxed">
                    {category.subtitle}
                  </p>
                </div>

                <div className="pt-4">
                  <Link
                    href={category.buttonLink}
                    className="inline-flex items-center bg-white text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 transform group-hover:scale-105 shadow-md"
                  >
                    {category.buttonText}
                    <svg
                      className="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
