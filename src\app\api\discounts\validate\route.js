import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Discount } from '@/libs/mongoDb/Models/Discount';
import { Order } from '@/libs/mongoDb/Models/Order';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    const { code, cartTotal, cartItems } = await request.json();

    if (!code) {
      return NextResponse.json(
        { error: 'Discount code is required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Find the discount
    const discount = await Discount.findValidDiscount(code);

    if (!discount) {
      return NextResponse.json(
        { error: 'Invalid or expired discount code' },
        { status: 404 }
      );
    }

    // Check minimum order amount
    if (discount.minimumOrderAmount > 0 && cartTotal < discount.minimumOrderAmount) {
      return NextResponse.json(
        { 
          error: `Minimum order amount of P${discount.minimumOrderAmount.toFixed(2)} required for this discount` 
        },
        { status: 400 }
      );
    }

    // Check user-specific restrictions if user is logged in
    if (session?.user) {
      const userId = session.user.id;

      // Check if discount is applicable to this user
      let isFirstTimeCustomer = false;
      if (discount.firstTimeCustomersOnly) {
        const orderCount = await Order.countDocuments({ userId });
        isFirstTimeCustomer = orderCount === 0;
      }

      if (!discount.isApplicableToUser(userId, isFirstTimeCustomer)) {
        return NextResponse.json(
          { error: 'This discount is not applicable to your account' },
          { status: 403 }
        );
      }

      // Check customer usage limit
      if (!discount.checkCustomerUsageLimit(userId)) {
        return NextResponse.json(
          { error: 'You have already used this discount code' },
          { status: 403 }
        );
      }
    }

    // Check product/category restrictions
    if (cartItems && cartItems.length > 0) {
      const hasApplicableItems = cartItems.some(item => {
        // Check if item is in applicable products
        if (discount.applicableProducts.length > 0) {
          return discount.applicableProducts.includes(item.productId);
        }

        // Check if item is in applicable categories
        if (discount.applicableCategories.length > 0) {
          return item.category && discount.applicableCategories.some(cat => 
            item.category.includes(cat)
          );
        }

        // Check if item is in applicable brands
        if (discount.applicableBrands.length > 0) {
          return discount.applicableBrands.includes(item.brand);
        }

        // Check exclusions
        if (discount.excludedProducts.includes(item.productId) ||
            (item.category && discount.excludedCategories.some(cat => item.category.includes(cat))) ||
            discount.excludedBrands.includes(item.brand)) {
          return false;
        }

        return true;
      });

      if (!hasApplicableItems) {
        return NextResponse.json(
          { error: 'This discount is not applicable to items in your cart' },
          { status: 400 }
        );
      }
    }

    // Calculate discount amount
    const discountAmount = discount.calculateDiscount(cartTotal, cartItems);

    return NextResponse.json({
      valid: true,
      discount: {
        id: discount._id,
        code: discount.code,
        name: discount.name,
        type: discount.type,
        value: discount.value,
        discountAmount,
        description: discount.description,
        freeShipping: discount.type === 'free_shipping'
      }
    });

  } catch (error) {
    console.error('Discount validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
