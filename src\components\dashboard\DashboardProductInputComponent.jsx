'use client'

import Image from "next/image"
import { useEffect, useState } from "react"
import { FaRegImage } from "react-icons/fa"
import BtnTrashComponent from "./BtnTrashComponent"
import { usePathname, useRouter } from "next/navigation"
import { deleteFiles,upload,uploadMulitple } from "@/libs/firebase/uploadFile"

function InputText({data,setInfoToSubmit}) {
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:e.target.value})
    // console.log(e.target.value)
  }
  // console.log('InputText:',)
  return(
    <input onChange={(e)=>handleChange(e)} className='flex px-4 w-full md:h-16 h-10 border-b-gray-200 border-b-[1px] outline-none' {...data}/>
  )
}

function InputRadio({data,setInfoToSubmit}) {
  const {state,...others}=data
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[others?.name]:true})
    // console.log('InputText:',[data?.name])
  }
  // console.log('InputRadio:',state)
  return(
    <fieldset onChange={(e)=>handleChange(e)} className="flex items-center capitalize w-fit gap-5 mt-5  md:h-16 h-10">
      {others?.list.map((item,index)=>
        <div key={index} className="flex w-fit gap-4 items-center">
          <label htmlFor="">{item?.name}</label>
          <input value={state==='true' && true} className='flex w-5 h-16 outline-none' type="radio"/>
        </div>
      )}
    </fieldset>
  )
}

function InputSelector({data,setInfoToSubmit}) {
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:e.target.value})
  }
  // console.log('InputSelector:',data)
  return(
    <div className="flex w-fit items-center  md:h-16 h-10 gap-4">
      <label className="capitalize" htmlFor="">{data?.name}</label>
      <select  onChange={(e)=>handleChange(e)} className="flex flex-col border-gray-200 border-[1px] gap-2 h-10 rounded px-5 items-center justify-center outline-none" name="" id="">
        {data?.options.map((item,index)=><option key={index} onChange={(e)=>handleChange(e)} className='flex w-full h-full rounded items-center' {...item}>{item?.name}</option>)}
      </select>
    </div>
  )
}

function InputAddToLIst({data,setInfoToSubmit}) {
  const {state,...others}=data
  const [list,setlist]=useState([])
  const [filteredList,setFilteredList]=useState([])
  const [userInfo,setUserInfo]=useState()
  const [deleteIndex,setDeleteIndex]=useState()

  const handleListFilter= (e,deleteIndex) => {
    e.preventDefault()
    setlist(prev=>prev.filter((item,index)=>item?.id!=deleteIndex?.id))
  }

  const handleClick = (e) => {
    e.preventDefault()
    setlist(prev=>prev=[...prev,{id:prev==undefined ? 0 : prev.length+1,item:userInfo}])
  }

  const updateList = () => {
    list?.length>0 ? setInfoToSubmit(prev=>prev={...prev,[data?.name]:list}) : setlist(prev=>state==undefined ? prev : state)
  }

  useEffect(()=>{updateList()},[list])

  // useEffect(()=>{setlist(state)},[])
  
  // console.log('InputAddToLIst:',{state:state},{list:list})
  return(
    <div className="flex relative flex-col p-1 rounded md:max-w-[45%] w-full mt-4 md:mt-0 items-center h-fit gap-4">
      <label className="capitalize underline text-start md:text-center w-full" htmlFor="">{others?.name}</label>
      <div className="flex w-fit h-fit items-end gap-2 px-4">
        <input onChange={(e)=>setUserInfo(e.target.value)} className='flex px-4 w-full md:h-10 h-10 border-b-gray-200 border-b-[1px] outline-none' {...others}/>
        <button onClick={handleClick} className="flex px-2 h-8 rounded bg-gray-100 ease-linear duration-300 text-xs items-center justify-center hover:bg-gray-300 shadow uppercase">add</button>
      </div>
      <div className="flex w-full flex-wrap min-h-4 overflow-y-auto gap-2 p-2  max-h-40 rounded bg-gray-100">
        {list?.map((item,index)=>
          <div className="flex w-fit gap-2 items-center shadow p-1 px-2 bg-gray-50 rounded h-fit" key={index}>
            <span>{item?.item}</span>
            <div onClick={(e)=>handleListFilter(e,item)} className="flex cursor-pointer items-center justify-center p-2 text-lg text-red-600 shadow rounded w-5 h-5">
              <BtnTrashComponent style={'text-sm'}/>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function InputImage({data,setInfoToSubmit,infoToSubmit}) {
  const [err,setErr]=useState('')
  const [file,setFile]=useState()
  const [fileArray,setFileArray]=useState([])
  const [progress,setProgress]=useState(0)
  const [showError,setShowError]=useState(false)
  const [pending,setPending]=useState(false)

  const handleUpload = () => {
    if(file){
      // for (let index = 0; index < fileArray.length; index++) {
      //   const element = fileArray[index];
      //   const uploadedFiles=element?.path.split('-')[element?.path.split('-').length-1]
      //   console.log(uploadedFiles,file)
      // }
      file.length>0 && uploadMulitple(file,setProgress,setPending,setFileArray,infoToSubmit?.title,fileArray)
      // setInfoToSubmit(prev=>{prev,{[data?.name]:fileArray}})
      // console.log(file)
    }
  }
  
  const handleUserInfoUpdate= () => {
    // setInfoToSubmit(prev=>{prev,{[data?.name]:fileArray}})
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:fileArray})
    console.log(infoToSubmit)
  }
  
  useEffect(()=>{handleUserInfoUpdate()},[fileArray])

  useEffect(()=>handleUpload(),[file])
  // useEffect(()=>handleUpload(),[fileArray])

  // console.log('InputImage:',fileArray,[data?.name])
  return(
    <div className="flex flex-col w-full items-center h-fit">
      {infoToSubmit?.title?.length>3 ? <input type="file" multiple onChange={(e)=>setFile(e.target.files)} className='flex w-full justify-center mt-5 items-center ml-[15%] md:h-16 h-10 outline-none' name="images"/> : <span className="flex w-full font-medium">Please enter product title to uplaod profile image</span>}
      {/* <button onClick={handleUpload} className="flex bg-gray-900 hover:bg-gray-700 duration-300 ease-linear text-white shadow w-full items-center justify-center uppercase h-10 rounded">upload</button> */}
      <span className="flex w-full items-center justify-center text-center font-medium">{progress}% loaded...</span>
      {progress==100 && <span className="flex w-full items-center justify-center text-center font-medium">upload complete</span>}
    </div>
  )
}

export default function DashboardProductInputComponent({data}) {

  const [infoToSubmit,setInfoToSubmit]=useState({
    images:[],
    title:'',
    brand:'',
    rating:'',
    thumbnail:'',
    warrantyInformation:'',
    shippingInformation:'',
    desc:'',
    returnPolicy:'',
    price:'',
    inStock:'',
    reviews:{},
    tags:[],
    category:[],
    size:[],
    color:[],
    coments:[],
  })
  const [err,setErr]=useState('')
  const [showError,setShowError]=useState(false)
  const [pending,setPending]=useState(false)
  const router=useRouter()
  const pathName=usePathname()

  useEffect(()=>{
    // setInfoToSubmit(data)
    setInfoToSubmit(data)
  },[data])

  const inputFeild={
    inputExt:[
      {type:"text",required:data?false:true,name:"title",placeholder:infoToSubmit?.title ? infoToSubmit?.title : "title"},
      {type:"text",required:data?false:true,name:"brand",placeholder:infoToSubmit?.brand ? infoToSubmit?.brand : "brand"},
      {type:"text",required:data?false:true,name:"desc",placeholder:infoToSubmit?.desc ? infoToSubmit?.desc : "desc"},
      {type:"number",required:data?false:true,name:"price",placeholder:infoToSubmit?.price ? infoToSubmit?.price : "price"},
      // {type:"text",name:"rating",placeholder:"rating"},
      // {type:"text",name:"thumbnail",placeholder:"thumbnail"},
      {type:"text",name:"directions",placeholder:infoToSubmit?.directions ? infoToSubmit?.directions :"directions"},
      {type:"text",name:"ingredients",placeholder:infoToSubmit?.ingredients ? infoToSubmit?.ingredients :"ingredients"},
      {type:"text",name:"warrantyInformation",placeholder:infoToSubmit?.warrantyInformation ? infoToSubmit?.warrantyInformation :"warranty Information"},
      {type:"text",name:"shippingInformation",placeholder:infoToSubmit?.shippingInformation ? infoToSubmit?.shippingInformation :"shipping Information"},
      {type:"text",name:"returnPolicy",placeholder:infoToSubmit?.returnPolicy ? infoToSubmit?.returnPolicy :"returnPolicy"},
    ],
    inputFiles:[
      {type:"file",name:"images"},
      // {type:"file",name:"image"},
    ],
    inputselector:[
      "role",
    ],
    inputAddList:[
      {state:infoToSubmit?.category,type:"text",required:data? false : true,name:"category",placeholder:"category*"},
      {state:infoToSubmit?.size,type:"text",name:"size",placeholder:"size"},
      {state:infoToSubmit?.color,type:"text",name:"color",placeholder:"color"},
      {state:infoToSubmit?.tags,type:"text",name:"tags",placeholder:"tags"},
    ],
    inputChecked:[
      {
        state:infoToSubmit?.inStock,
        name:'inStock',
        list:[
          {type:"radio",name:"in Stock",value:"inStock"}
        ],
      },
    ],
    inputSelector:[
      {
        name:'select user role',
        options:[
          // {type:"options",name:"select user role",value:""},
          {type:"options",name:"client",value:"client"},
          {type:"options",name:"visitor",value:"visitor"}
        ],
      },
    ],
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setPending(true)
    try {
      const res=await fetch(`/api/products`,{method:'POST',body:JSON.stringify(infoToSubmit)})
      if(res.ok){
        setPending(false)
        setShowError(true)
        // setErr(await res.json())
        router.push('/dashboard/products')
      }else{
        setShowError(true)
        setErr(await res.json())
        setPending(false)
      }
    } catch (error) {
      console.log(error)
      setShowError(true)
      setErr(error)
      setPending(false)
    }
  }

  const handleUpdate = async (e) => {
    e.preventDefault()
    setPending(true)
    try {
      const res=await fetch(`/api/products/${data?._id}`,{method:'PUT',body:JSON.stringify(infoToSubmit)})
      if(res.ok){
        setPending(false)
        setShowError(true)
        // setErr(await res.json())
        router.push('/dashboard/products')
      }else{
        setShowError(true)
        setErr(await res.json())
        setPending(false)
      }
    } catch (error) {
      console.log(error)
      setShowError(true)
      setErr(error)
      setPending(false)
    }
  }

  const handleDelete = async (e,data) => {
    e.preventDefault()
    // console.log(data?.id)
    // setPending(true)
    // setInfoToSubmit(prev=>{prev?.images.filter((img,index)=>img?.id!==data?.id)})
    // deleteFiles(data?.path)
  }

  // console.log('DashboardProductInputComponent:',{infoToSubmit:infoToSubmit},{data:data})
  return (
     <form onSubmit={data ? handleUpdate : handleSubmit} className='flex flex-col w-full h-full border-[1px] rounded-lg bg-white shadow-lg gap-2 border-gray-300 items-center'>
      <div className='flex flex-col md:flex-row w-full h-[calc(100%-72px)] gap-2 items-center justify-center'>
        {/* <div className="imageContainerWrap flex flex-col md:w-1/3 h-[400px] md:h-full w-full md:ml-5 ml-0 md:py-5 py-2 px-2 gap-4"> */}
        <div className="imageContainerWrap flex md:w-1/3 h-[400px] md:h-full w-full md:ml-5 ml-0 md:py-5 py-2 px-2 gap-4">
          <div className="imageContainer flex relative w-full min-h-[80%] items-center justify-center bg-gray-100">
            {/* {infoToSubmit?.images
              ? <div className="flex gap-2 bg-gray-100 w-fit h-full">
                  {infoToSubmit?.images?.map((img,index)=>
                    <div key={index} className="flex relative w-full flex-none h-full">
                      <Image className="object-contain" src={img?.image} alt="profile image" fill/>
                      <div onClick={(e)=>handleDelete(e,img)} className="flex absolute w-5 h-5 rounded p-2 shadow bg-gray-50 items-center justify-center z-10 left-0 right-0 mx-auto bottom-4">
                        <BtnTrashComponent style={'text-sm'}/>
                      </div>
                    </div>
                  )} 
                </div> 
              : <FaRegImage className="text-5xl"/>} */}
              top images
          </div>
          <div className="flex h-fit w-full">
            {inputFeild.inputFiles.map((item,index)=>
              <InputImage key={index} infoToSubmit={infoToSubmit} setInfoToSubmit={setInfoToSubmit} data={item}/>
            )}
          </div>
        </div>
        <div className='flex flex-col md:w-2/3 h-2/3 md:h-[calc(100%-20px)] md:px-10 px-2 overflow-y-auto over' onSubmit={handleSubmit}>
          <div className="flex flex-col gap-2 w-full h-fit">
            {inputFeild.inputExt.map((item,index)=>
              <InputText key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
            )}
            <div className="flex flex-col md:flex-row md:h-20 h-fit justify-between md:items-end items-start w-fit md:gap-20">
              {inputFeild.inputChecked.map((item,index)=>
                <InputRadio key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )}
              {/* {inputFeild.inputSelector.map((item,index)=>
                <InputSelector key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )} */}
            </div>
            <div className="flex h-fit gap-5 w-full flex-wrap">
              {inputFeild.inputAddList.map((item,index)=>
                <InputAddToLIst key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )}
            </div>
          </div>
          {showError && <span className="flex text-xl font-medium">{err}</span>}
        </div>
      </div>
      <input className="flex w-fit px-28 h-12 bg-gray-900 hover:bg-gray-700 mb-4 rounded-md text-white duration-300 ease-linear" type="submit"/>
    </form>
  )
}