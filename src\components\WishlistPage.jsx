'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  HeartIcon, 
  ShoppingCartIcon, 
  TrashIcon, 
  ShareIcon,
  StarIcon
} from 'lucide-react';

export default function WishlistPage({ userId }) {
  const [wishlist, setWishlist] = useState(null);
  const [loading, setLoading] = useState(true);
  const [removingItems, setRemovingItems] = useState(new Set());

  useEffect(() => {
    fetchWishlist();
  }, []);

  const fetchWishlist = async () => {
    try {
      const response = await fetch('/api/wishlist');
      if (response.ok) {
        const data = await response.json();
        setWishlist(data.wishlist);
      }
    } catch (error) {
      console.error('Error fetching wishlist:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRemoveItem = async (productId) => {
    setRemovingItems(prev => new Set(prev).add(productId));
    
    try {
      const response = await fetch('/api/wishlist/remove', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ productId }),
      });

      if (response.ok) {
        setWishlist(prev => ({
          ...prev,
          items: prev.items.filter(item => item.productId._id !== productId)
        }));
      }
    } catch (error) {
      console.error('Error removing item:', error);
    } finally {
      setRemovingItems(prev => {
        const newSet = new Set(prev);
        newSet.delete(productId);
        return newSet;
      });
    }
  };

  const handleAddToCart = async (product) => {
    try {
      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: product._id,
          quantity: 1,
          price: product.price,
        }),
      });

      if (response.ok) {
        // Show success message or update cart state
        console.log('Added to cart successfully');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  const handleMoveToCart = async (product) => {
    await handleAddToCart(product);
    await handleRemoveItem(product._id);
  };

  const renderStars = (rating, count) => {
    return (
      <div className="flex items-center space-x-1">
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <StarIcon
              key={star}
              className={`w-4 h-4 ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
              }`}
            />
          ))}
        </div>
        {count > 0 && (
          <span className="text-sm text-gray-600">({count})</span>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="h-80 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">My Wishlist</h1>
        {wishlist?.items?.length > 0 && (
          <div className="flex items-center space-x-4">
            <span className="text-gray-600">
              {wishlist.items.length} item{wishlist.items.length !== 1 ? 's' : ''}
            </span>
            <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
              <ShareIcon className="w-5 h-5" />
              <span>Share Wishlist</span>
            </button>
          </div>
        )}
      </div>

      {wishlist?.items?.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {wishlist.items.map((item) => {
            const product = item.productId;
            const isRemoving = removingItems.has(product._id);
            
            return (
              <div
                key={item._id}
                className={`bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-all duration-300 ${
                  isRemoving ? 'opacity-50' : ''
                }`}
              >
                {/* Product Image */}
                <div className="relative aspect-square bg-gray-100">
                  <Link href={`/products/${product.slug}`}>
                    {product.thumbnail || product.images?.[0] ? (
                      <Image
                        src={product.thumbnail || product.images[0]}
                        alt={product.title}
                        fill
                        className="object-cover hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400">
                        No image
                      </div>
                    )}
                  </Link>

                  {/* Remove Button */}
                  <button
                    onClick={() => handleRemoveItem(product._id)}
                    disabled={isRemoving}
                    className="absolute top-2 right-2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-colors disabled:opacity-50"
                  >
                    <TrashIcon className="w-4 h-4 text-gray-600" />
                  </button>

                  {/* Sale Badge */}
                  {product.onSale && (
                    <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
                      Sale
                    </div>
                  )}
                </div>

                {/* Product Info */}
                <div className="p-4 space-y-3">
                  <div className="text-sm text-gray-500">{product.brand}</div>
                  
                  <h3 className="font-medium text-gray-900 line-clamp-2">
                    <Link 
                      href={`/products/${product.slug}`}
                      className="hover:text-blue-600 transition-colors"
                    >
                      {product.title}
                    </Link>
                  </h3>

                  {/* Rating */}
                  {product.ratingCount > 0 && renderStars(product.rating, product.ratingCount)}

                  {/* Price */}
                  <div className="flex items-center space-x-2">
                    <span className="font-semibold text-gray-900">
                      P{product.price.toFixed(2)}
                    </span>
                    {product.salePrice && (
                      <span className="text-sm text-gray-500 line-through">
                        P{product.salePrice.toFixed(2)}
                      </span>
                    )}
                  </div>

                  {/* Stock Status */}
                  <div className="text-sm">
                    {product.inStock ? (
                      <span className="text-green-600 font-medium">In Stock</span>
                    ) : (
                      <span className="text-red-600 font-medium">Out of Stock</span>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2 pt-2">
                    <button
                      onClick={() => handleMoveToCart(product)}
                      disabled={!product.inStock || isRemoving}
                      className="w-full bg-gray-900 text-white py-2 px-4 rounded-lg font-medium hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                    >
                      <ShoppingCartIcon className="w-4 h-4" />
                      <span>Move to Cart</span>
                    </button>
                    
                    <button
                      onClick={() => handleAddToCart(product)}
                      disabled={!product.inStock || isRemoving}
                      className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors"
                    >
                      Add to Cart
                    </button>
                  </div>

                  {/* Added Date */}
                  <div className="text-xs text-gray-500 pt-2 border-t">
                    Added {new Date(item.addedAt).toLocaleDateString()}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-12">
          <HeartIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Your wishlist is empty</h3>
          <p className="text-gray-600 mb-6">
            Save products you love to your wishlist and never lose track of them.
          </p>
          <Link
            href="/products"
            className="inline-block bg-gray-900 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
          >
            Discover Products
          </Link>
        </div>
      )}
    </div>
  );
}
