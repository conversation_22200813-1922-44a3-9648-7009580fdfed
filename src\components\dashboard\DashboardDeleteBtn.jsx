'use client'
import { deleteFiles } from '@/lib/firebase/uploadFile'
import React from 'react'
import { FaRegTrashCan } from 'react-icons/fa6'

export default function DashboardDeleteBtn({data}) {
    // const deleteProduct = async (data) => {
    //     try {
    //       // const res=await fetch(`${settings.url.dev}/api/ecommerce/products/${id}`,{method:'DELETE'})
    //       data?.images.forEach(element => {
    //         deleteFiles(element.path)
    //       });
    //     } catch (error) {
    //       console.log(error)
    //     }
    //   }
    // console.log('DashboardDeleteBtn:',data)
  return (
    <FaRegTrashCan onClick={(e)=>deleteProduct(e,data)} className='w-8 h-8 cursor-pointer rounded p-2 bg-gray-50 shadow bottom-1 border-gray-100 text-red-500' />
  )
}
