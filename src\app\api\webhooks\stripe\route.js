import { NextResponse } from 'next/server';
import { headers } from 'next/headers';
import <PERSON><PERSON> from 'stripe';
import { Order } from '@/libs/mongoDb/Models/Order';
import { Product } from '@/libs/mongoDb/Models/Products';
import { User } from '@/libs/mongoDb/Models/User';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';
import { sendEmail } from '@/libs/email/emailService';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

export async function POST(request) {
  try {
    const body = await request.text();
    const headersList = headers();
    const sig = headersList.get('stripe-signature');

    let event;

    try {
      event = stripe.webhooks.constructEvent(body, sig, endpointSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err.message);
      return NextResponse.json(
        { error: 'Webhook signature verification failed' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Handle the event
    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSucceeded(event.data.object);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentFailed(event.data.object);
        break;

      case 'payment_intent.canceled':
        await handlePaymentCanceled(event.data.object);
        break;

      case 'charge.dispute.created':
        await handleChargeDispute(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object);
        break;

      case 'customer.subscription.created':
      case 'customer.subscription.updated':
      case 'customer.subscription.deleted':
        await handleSubscriptionChange(event.data.object, event.type);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });

  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json(
      { error: 'Webhook handler failed' },
      { status: 500 }
    );
  }
}

async function handlePaymentSucceeded(paymentIntent) {
  try {
    // Find the order by payment intent ID
    const order = await Order.findOne({ 
      stripePaymentIntentId: paymentIntent.id 
    }).populate('userId', 'name email');

    if (!order) {
      console.error('Order not found for payment intent:', paymentIntent.id);
      return;
    }

    // Update order status
    order.paymentStatus = 'paid';
    order.status = 'confirmed';
    order.confirmedAt = new Date();
    order.stripeChargeId = paymentIntent.latest_charge;

    await order.save();

    // Update inventory
    await updateInventory(order.items, 'decrease');

    // Send order confirmation email
    if (order.userId) {
      try {
        await sendEmail(order.email, 'orderConfirmation', {
          order,
          user: order.userId
        });
      } catch (emailError) {
        console.error('Order confirmation email error:', emailError);
      }
    }

    console.log('Payment succeeded for order:', order.orderNumber);

  } catch (error) {
    console.error('Error handling payment succeeded:', error);
  }
}

async function handlePaymentFailed(paymentIntent) {
  try {
    const order = await Order.findOne({ 
      stripePaymentIntentId: paymentIntent.id 
    }).populate('userId', 'name email');

    if (!order) {
      console.error('Order not found for failed payment:', paymentIntent.id);
      return;
    }

    // Update order status
    order.paymentStatus = 'failed';
    order.status = 'cancelled';
    order.cancelledAt = new Date();
    order.cancellationReason = 'Payment failed';

    await order.save();

    // Send payment failed email
    if (order.userId) {
      try {
        await sendEmail(order.email, 'paymentFailed', {
          order,
          user: order.userId,
          retryUrl: `${process.env.NEXTAUTH_URL}/checkout?retry=${order._id}`
        });
      } catch (emailError) {
        console.error('Payment failed email error:', emailError);
      }
    }

    console.log('Payment failed for order:', order.orderNumber);

  } catch (error) {
    console.error('Error handling payment failed:', error);
  }
}

async function handlePaymentCanceled(paymentIntent) {
  try {
    const order = await Order.findOne({ 
      stripePaymentIntentId: paymentIntent.id 
    });

    if (!order) {
      console.error('Order not found for canceled payment:', paymentIntent.id);
      return;
    }

    // Update order status
    order.paymentStatus = 'cancelled';
    order.status = 'cancelled';
    order.cancelledAt = new Date();
    order.cancellationReason = 'Payment cancelled by customer';

    await order.save();

    console.log('Payment cancelled for order:', order.orderNumber);

  } catch (error) {
    console.error('Error handling payment canceled:', error);
  }
}

async function handleChargeDispute(dispute) {
  try {
    // Find order by charge ID
    const order = await Order.findOne({ 
      stripeChargeId: dispute.charge 
    }).populate('userId', 'name email');

    if (!order) {
      console.error('Order not found for dispute:', dispute.charge);
      return;
    }

    // Update order with dispute information
    order.disputeStatus = 'disputed';
    order.disputeReason = dispute.reason;
    order.disputeAmount = dispute.amount;
    order.disputedAt = new Date();

    await order.save();

    // Notify admin about dispute
    console.log('Charge dispute created for order:', order.orderNumber);
    // TODO: Send admin notification email

  } catch (error) {
    console.error('Error handling charge dispute:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice) {
  try {
    // Handle subscription payments
    if (invoice.subscription) {
      const user = await User.findOne({ 
        stripeCustomerId: invoice.customer 
      });

      if (user) {
        // Update user subscription status
        user.subscriptionStatus = 'active';
        user.subscriptionPeriodEnd = new Date(invoice.period_end * 1000);
        await user.save();

        console.log('Subscription payment succeeded for user:', user.email);
      }
    }

  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleSubscriptionChange(subscription, eventType) {
  try {
    const user = await User.findOne({ 
      stripeCustomerId: subscription.customer 
    });

    if (!user) {
      console.error('User not found for subscription:', subscription.id);
      return;
    }

    switch (eventType) {
      case 'customer.subscription.created':
        user.subscriptionStatus = 'active';
        user.subscriptionId = subscription.id;
        user.subscriptionPeriodEnd = new Date(subscription.current_period_end * 1000);
        break;

      case 'customer.subscription.updated':
        user.subscriptionStatus = subscription.status;
        user.subscriptionPeriodEnd = new Date(subscription.current_period_end * 1000);
        break;

      case 'customer.subscription.deleted':
        user.subscriptionStatus = 'cancelled';
        user.subscriptionCancelledAt = new Date();
        break;
    }

    await user.save();
    console.log(`Subscription ${eventType} for user:`, user.email);

  } catch (error) {
    console.error('Error handling subscription change:', error);
  }
}

async function updateInventory(items, operation) {
  try {
    for (const item of items) {
      const product = await Product.findById(item.productId);
      
      if (product) {
        if (operation === 'decrease') {
          product.stock = Math.max(0, product.stock - item.quantity);
          product.soldCount = (product.soldCount || 0) + item.quantity;
        } else if (operation === 'increase') {
          product.stock += item.quantity;
          product.soldCount = Math.max(0, (product.soldCount || 0) - item.quantity);
        }

        // Update stock status
        product.inStock = product.stock > 0;
        
        await product.save();
      }
    }
  } catch (error) {
    console.error('Error updating inventory:', error);
  }
}
