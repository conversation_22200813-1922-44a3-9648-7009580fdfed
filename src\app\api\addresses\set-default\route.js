import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Address } from '@/libs/mongoDb/Models/Address';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { addressId, type } = await request.json();

    if (!addressId || !type) {
      return NextResponse.json(
        { error: 'Address ID and type are required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Find the address and verify ownership
    const address = await Address.findOne({ 
      _id: addressId, 
      userId: session.user.id 
    });

    if (!address) {
      return NextResponse.json(
        { error: 'Address not found' },
        { status: 404 }
      );
    }

    // Remove default flag from all addresses of this type
    await Address.updateMany(
      { 
        userId: session.user.id, 
        type: type 
      },
      { isDefault: false }
    );

    // Set this address as default
    address.isDefault = true;
    await address.save();

    // Get all updated addresses
    const addresses = await Address.find({ userId: session.user.id })
      .sort({ isDefault: -1, createdAt: -1 })
      .lean();

    return NextResponse.json({
      message: 'Default address updated successfully',
      addresses
    });

  } catch (error) {
    console.error('Set default address error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
