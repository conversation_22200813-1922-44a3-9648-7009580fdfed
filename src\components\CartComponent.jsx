'use client'
import { ACTIONS_CART } from '@/lib/amieshop/context/reducerCart'
import { useCartContext } from '@/lib/amieshop/context/useCartContext'
import { settings } from '@/lib/amieshop/siteEcomSettimngs'
import Image from 'next/image'
import React, { useEffect, useState } from 'react'
import { FaRegTrashAlt } from 'react-icons/fa'
import { IoCloseOutline } from 'react-icons/io5'

const ItemList = ({data,setlist,list}) => {
    const {cartState,showWishList,setshowWishList,cartDispatch}=useCartContext()
    const [count,setcount]=useState(1)

    const handleCount = (type) => {
        // console.log('handleCount:-',type)
        type=='-' ? setcount(prev=>prev>1 ? prev-1 : 1 ) : setcount(prev=>prev>0 ? prev+1 : 1 ) 
    }

    const handleDelete = (id) => {
        // console.log('handle delete',id)
        setlist(prev=>prev=list?.filter((item,index)=>item.id!=id))
    }

    // console.log('ItemList:-')
  return(
    <div className='flex relative w-full h-24 items-center justify-between gap-2'>
        <div className='flex relative shadow rounded overflow-hidden w-20 h-20'>
            <Image className='object-cover ' src={data.img} fill alt='product link image'/>
        </div>
        <div className='flex flex-col w-fit gap-1'>
            <span className='capitalize font-bold'>{data.title}</span>
            <span className='text-xs'>{data.desc.slice(0,50)}...</span>
            <div className='flex text-blue-400 items-center gap-2 w-full justify-between'>
                <div className='flex items-center w-fit gap-2'>
                    <div className='flex relative w-full items-center h-fit gap-2'>
                        <div onClick={()=>handleCount('-')} className='flex w-6 select-none h-6 rounded shadow bg-gray-200 cursor-pointer text-gray-600 items-center justify-center'>-</div>
                        <span className='text-sm font-light'>{count}</span>
                        <div onClick={()=>handleCount('+')} className='flex w-6 select-none h-6 rounded shadow bg-gray-200 cursor-pointer text-gray-600 items-center justify-center'>+</div>
                    </div>
                    <span className='text-nowrap'>{`x $${30}`}</span>
                </div>
                <div className='flex text-gray-600 items-end gap-1 w-fit'>
                    <span className='text-sm font-medium capitalize'>total:</span>
                    <span className='text-xl'>${count*30}</span>
                </div>
            </div>
        </div>
        <div className='flex flex-col w-fit text-xl mr-4 items-end gap-4'>
            {/* <MdOutlineShoppingCart onClick={()=>HandleAddToCart(data?.id)} className='cursor-pointer'/> */}
            <FaRegTrashAlt onClick={()=>handleDelete(data?.id)} className='cursor-pointer text-red-500'/>
        </div>
    </div>
  )
}

export default function CartComponent() {
    const {showCart,setshowCart,cartDispatch,cartState}=useCartContext()
    const [list,setlist]=useState([])
    useEffect(()=>{
        setlist(settings?.product.slice(0,15))
    },[])

    const HandleChecout = () => {
        // setshowCart(!showCart)
        cartDispatch({type:ACTIONS_CART.TOGGLE_CART})
    }
    
    // console.log('CartComponent:-',list)
  return (
    <div className={`${cartState?.showCart ? 'flex' : 'hidden'} z-50 fixed md:top-4 top-0 md:right-8 right-0 md:shadow-xl md:w-[420px] w-[calc(100%-16px)] h-full md:h-[calc(100%-24px)] border-[1px] border-gray-100 bg-white p-5 md:rounded-lg overflow-hidden`}>
        <div className='flex relative flex-col w-full min-h-full text-gray-500'>
            <p className='flex text-2xl'>Item in your cart</p>
            <hr className='w-full border-[1px] border-gray-300 px-5 my-2'/>

            <button onClick={()=>cartDispatch({type:ACTIONS_CART.TOGGLE_CART})} className='flex absolute -top-0 right-0 hover:scale-105 duration-300 ease-linear hover:bg-gray-200 max-w-min p-1 rounded text-xl shadow bg-gray-100'><IoCloseOutline /></button>
            
            <div className='flex flex-col w-full select-none flex-[6] gap-5 overflow-y-auto'>
                {list.map((item,index)=>
                    <ItemList key={item?.id} data={item} setlist={setlist} list={list}/>
                )}
            </div>
            <hr className='w-full border-[1px] border-gray-300 px-5 my-2'/>
            <div className='flex flex-col w-full flex-[1] gap-4'>
                <div className='flex w-full h-fit  text-gray-700 items-end gap-4'>
                    <span className='text-lg uppercase'>subtotal:</span>
                    <span className='text-3xl font-medium'>$400</span>
                </div>
                <button onClick={HandleChecout} className='flex relative items-center text-white justify-center gap-4 rounded shadow bg-blue-500 cursor-pointer w-full font-medium uppercase h-10'>proceed to checkout</button>
            <span className='text-red-500 capitalize cursor-pointer underline font-medium'>reset cart</span>
        </div>
      </div>
    </div>
  )
}
