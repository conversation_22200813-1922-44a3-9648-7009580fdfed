'use client';

import Link from 'next/link';
import Image from 'next/image';
import { StarIcon, HeartIcon, ShoppingCartIcon } from 'lucide-react';

export default function RelatedProducts({ products, title = "Related Products" }) {
  const renderStars = (rating, count) => {
    return (
      <div className="flex items-center space-x-1">
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <StarIcon
              key={star}
              className={`w-4 h-4 ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
              }`}
            />
          ))}
        </div>
        {count > 0 && (
          <span className="text-sm text-gray-600">({count})</span>
        )}
      </div>
    );
  };

  const ProductCard = ({ product }) => (
    <div className="group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden bg-gray-100">
        <Link href={`/products/${product.slug}`}>
          {product.thumbnail || product.images?.[0] ? (
            <Image
              src={product.thumbnail || product.images[0]}
              alt={product.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-400">
              No image
            </div>
          )}
        </Link>
        
        {/* Sale Badge */}
        {product.onSale && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
            Sale
          </div>
        )}
        
        {/* New Badge */}
        {product.newProduct && (
          <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 text-xs font-semibold rounded">
            New
          </div>
        )}

        {/* Hover Actions */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
          <button className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors">
            <HeartIcon className="w-5 h-5 text-gray-700" />
          </button>
          <button className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors">
            <ShoppingCartIcon className="w-5 h-5 text-gray-700" />
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4 space-y-2">
        <div className="text-sm text-gray-500">{product.brand}</div>
        <h3 className="font-medium text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
          <Link href={`/products/${product.slug}`}>
            {product.title}
          </Link>
        </h3>
        
        {/* Rating */}
        {product.ratingCount > 0 && renderStars(product.rating, product.ratingCount)}
        
        {/* Price */}
        <div className="flex items-center space-x-2">
          <span className="font-semibold text-gray-900">
            P{product.price.toFixed(2)}
          </span>
          {product.salePrice && (
            <span className="text-sm text-gray-500 line-through">
              P{product.salePrice.toFixed(2)}
            </span>
          )}
        </div>

        {/* Skin Type Tags */}
        {product.skinType && product.skinType.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {product.skinType.slice(0, 2).map((type) => (
              <span
                key={type}
                className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
              >
                {type}
              </span>
            ))}
            {product.skinType.length > 2 && (
              <span className="text-xs text-gray-500">
                +{product.skinType.length - 2} more
              </span>
            )}
          </div>
        )}
      </div>
    </div>
  );

  if (!products || products.length === 0) {
    return null;
  }

  return (
    <section className="border-t pt-16">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
        <Link
          href="/products"
          className="text-gray-600 hover:text-gray-900 font-medium transition-colors"
        >
          View all →
        </Link>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.slice(0, 8).map((product) => (
          <ProductCard key={product._id} product={product} />
        ))}
      </div>

      {/* Show More Button for Mobile */}
      {products.length > 4 && (
        <div className="text-center mt-8 sm:hidden">
          <Link
            href="/products"
            className="inline-block bg-gray-900 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
          >
            View All Products
          </Link>
        </div>
      )}
    </section>
  );
}
