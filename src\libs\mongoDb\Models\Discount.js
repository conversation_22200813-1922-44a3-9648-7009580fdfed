import mongoose from 'mongoose';
const { Schema } = mongoose;

const discountSchema = new Schema({
  code: { 
    type: String, 
    required: true, 
    unique: true, 
    uppercase: true,
    trim: true,
    index: true
  },
  
  // Discount Details
  name: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  
  // Discount Type and Value
  type: { 
    type: String, 
    enum: ['percentage', 'fixed', 'bogo', 'free_shipping'], 
    required: true 
  },
  value: { type: Number, required: true }, // Percentage (0-100) or fixed amount
  
  // BOGO specific fields
  bogoConfig: {
    buyQuantity: { type: Number, default: 1 },
    getQuantity: { type: Number, default: 1 },
    applicableProducts: [{ type: Schema.Types.ObjectId, ref: 'Product' }],
    applicableCategories: [String],
  },
  
  // Usage Restrictions
  minimumOrderAmount: { type: Number, default: 0 },
  maximumDiscountAmount: { type: Number }, // Cap for percentage discounts
  usageLimit: { type: Number }, // Total usage limit
  usageLimitPerCustomer: { type: Number, default: 1 },
  
  // Applicable Products/Categories
  applicableProducts: [{ type: Schema.Types.ObjectId, ref: 'Product' }],
  applicableCategories: [String],
  applicableBrands: [String],
  
  // Excluded Products/Categories
  excludedProducts: [{ type: Schema.Types.ObjectId, ref: 'Product' }],
  excludedCategories: [String],
  excludedBrands: [String],
  
  // Customer Restrictions
  firstTimeCustomersOnly: { type: Boolean, default: false },
  applicableCustomers: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  excludedCustomers: [{ type: Schema.Types.ObjectId, ref: 'User' }],
  
  // Date Restrictions
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  
  // Status
  isActive: { type: Boolean, default: true },
  
  // Usage Tracking
  usageCount: { type: Number, default: 0 },
  usageHistory: [{
    userId: { type: Schema.Types.ObjectId, ref: 'User' },
    orderId: { type: Schema.Types.ObjectId, ref: 'Order' },
    discountAmount: { type: Number },
    usedAt: { type: Date, default: Date.now }
  }],
  
  // Metadata
  createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
  tags: [String], // For categorizing discounts
  internalNotes: { type: String }, // Admin notes
}, { timestamps: true });

// Indexes for performance
discountSchema.index({ code: 1, isActive: 1 });
discountSchema.index({ startDate: 1, endDate: 1 });
discountSchema.index({ type: 1 });

// Virtual for checking if discount is currently valid
discountSchema.virtual('isCurrentlyValid').get(function() {
  const now = new Date();
  return this.isActive && 
         this.startDate <= now && 
         this.endDate >= now &&
         (!this.usageLimit || this.usageCount < this.usageLimit);
});

// Method to check if discount is applicable to a user
discountSchema.methods.isApplicableToUser = function(userId, isFirstTimeCustomer = false) {
  // Check first-time customer restriction
  if (this.firstTimeCustomersOnly && !isFirstTimeCustomer) {
    return false;
  }
  
  // Check specific customer inclusions
  if (this.applicableCustomers.length > 0 && 
      !this.applicableCustomers.includes(userId)) {
    return false;
  }
  
  // Check customer exclusions
  if (this.excludedCustomers.includes(userId)) {
    return false;
  }
  
  return true;
};

// Method to check usage limit per customer
discountSchema.methods.checkCustomerUsageLimit = function(userId) {
  if (!this.usageLimitPerCustomer) return true;
  
  const customerUsage = this.usageHistory.filter(
    usage => usage.userId.toString() === userId.toString()
  ).length;
  
  return customerUsage < this.usageLimitPerCustomer;
};

// Method to calculate discount amount
discountSchema.methods.calculateDiscount = function(orderTotal, items = []) {
  if (!this.isCurrentlyValid) return 0;
  
  let discountAmount = 0;
  
  switch (this.type) {
    case 'percentage':
      discountAmount = (orderTotal * this.value) / 100;
      if (this.maximumDiscountAmount) {
        discountAmount = Math.min(discountAmount, this.maximumDiscountAmount);
      }
      break;
      
    case 'fixed':
      discountAmount = Math.min(this.value, orderTotal);
      break;
      
    case 'free_shipping':
      // This would be handled separately in shipping calculation
      discountAmount = 0;
      break;
      
    case 'bogo':
      discountAmount = this.calculateBogoDiscount(items);
      break;
      
    default:
      discountAmount = 0;
  }
  
  return Math.max(0, discountAmount);
};

// Method to calculate BOGO discount
discountSchema.methods.calculateBogoDiscount = function(items) {
  if (!this.bogoConfig || !items.length) return 0;
  
  // Filter applicable items
  const applicableItems = items.filter(item => {
    if (this.bogoConfig.applicableProducts.length > 0) {
      return this.bogoConfig.applicableProducts.includes(item.productId);
    }
    if (this.bogoConfig.applicableCategories.length > 0) {
      return item.category && this.bogoConfig.applicableCategories.some(cat => 
        item.category.includes(cat)
      );
    }
    return true;
  });
  
  if (applicableItems.length === 0) return 0;
  
  // Sort by price (descending) to give discount on cheaper items
  applicableItems.sort((a, b) => b.price - a.price);
  
  let totalDiscount = 0;
  let buyCount = 0;
  
  for (const item of applicableItems) {
    buyCount += item.quantity;
    
    // Calculate how many free items this purchase qualifies for
    const freeItems = Math.floor(buyCount / this.bogoConfig.buyQuantity) * this.bogoConfig.getQuantity;
    
    if (freeItems > 0) {
      // Apply discount to the cheapest qualifying items
      const discountQuantity = Math.min(freeItems, item.quantity);
      totalDiscount += discountQuantity * item.price;
    }
  }
  
  return totalDiscount;
};

// Method to record usage
discountSchema.methods.recordUsage = function(userId, orderId, discountAmount) {
  this.usageCount += 1;
  this.usageHistory.push({
    userId,
    orderId,
    discountAmount,
    usedAt: new Date()
  });
  
  return this.save();
};

// Static method to find valid discount by code
discountSchema.statics.findValidDiscount = function(code) {
  const now = new Date();
  return this.findOne({
    code: code.toUpperCase(),
    isActive: true,
    startDate: { $lte: now },
    endDate: { $gte: now },
    $or: [
      { usageLimit: { $exists: false } },
      { $expr: { $lt: ['$usageCount', '$usageLimit'] } }
    ]
  });
};

export const Discount = mongoose.models.Discount || mongoose.model('Discount', discountSchema);
