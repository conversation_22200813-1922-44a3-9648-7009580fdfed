'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { StarIcon, HeartIcon, ShoppingCartIcon } from 'lucide-react';

// Mock data - replace with actual API call
const mockProducts = [
  {
    id: 1,
    title: "SPF50 Sunscreen",
    brand: "Nakolwethu",
    price: 215.00,
    salePrice: null,
    rating: 5.0,
    ratingCount: 1,
    image: "/images/spf50-sunscreen.jpg",
    inStock: true,
    featured: true,
    category: "Sun Protection"
  },
  {
    id: 2,
    title: "Baobab Milk Toner",
    brand: "Fundamentals Skincare",
    price: 110.00,
    salePrice: null,
    rating: 5.0,
    ratingCount: 1,
    image: "/images/baobab-toner.jpg",
    inStock: true,
    featured: true,
    category: "Toners"
  },
  {
    id: 3,
    title: "Hypochlorous Acid Daily Spritz",
    brand: "Silki",
    price: 125.00,
    salePrice: null,
    rating: 0,
    ratingCount: 0,
    image: "/images/daily-spritz.jpg",
    inStock: true,
    featured: true,
    category: "Mists"
  },
  {
    id: 4,
    title: "2% Alpha Arbutin with 1% Hyaluronic Acid & 5% Vitamin C Serum",
    brand: "SKIN Functional",
    price: 160.00,
    salePrice: 190.00,
    rating: 0,
    ratingCount: 0,
    image: "/images/alpha-arbutin-serum.jpg",
    inStock: true,
    featured: true,
    category: "Serums",
    onSale: true
  }
];

export default function FeaturedProducts() {
  const [products, setProducts] = useState(mockProducts);
  const [activeTab, setActiveTab] = useState('spring');

  const tabs = [
    { id: 'spring', label: 'Spring skincare faves', products: mockProducts },
    { id: 'new', label: 'New Products Alert', products: mockProducts.slice(0, 3) },
    { id: 'popular', label: 'Popular Products', products: mockProducts }
  ];

  const renderStars = (rating, count) => {
    return (
      <div className="flex items-center space-x-1">
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <StarIcon
              key={star}
              className={`w-4 h-4 ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
              }`}
            />
          ))}
        </div>
        <span className="text-sm text-gray-600">({count})</span>
      </div>
    );
  };

  const ProductCard = ({ product }) => (
    <div className="group relative bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
      {/* Product Image */}
      <div className="relative aspect-square overflow-hidden bg-gray-100">
        <div className="w-full h-full flex items-center justify-center text-gray-400">
          Product Image
        </div>
        
        {/* Sale Badge */}
        {product.onSale && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
            Sale
          </div>
        )}
        
        {/* Sold Out Badge */}
        {!product.inStock && (
          <div className="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 text-xs font-semibold rounded">
            Sold out
          </div>
        )}

        {/* Hover Actions */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
          <button className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors">
            <HeartIcon className="w-5 h-5 text-gray-700" />
          </button>
          <button className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors">
            <ShoppingCartIcon className="w-5 h-5 text-gray-700" />
          </button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4 space-y-2">
        <div className="text-sm text-gray-500">{product.brand}</div>
        <h3 className="font-medium text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
          <Link href={`/products/${product.id}`}>
            {product.title}
          </Link>
        </h3>
        
        {/* Rating */}
        {product.ratingCount > 0 && renderStars(product.rating, product.ratingCount)}
        
        {/* Price */}
        <div className="flex items-center space-x-2">
          <span className="font-semibold text-gray-900">
            P{product.price.toFixed(2)}
          </span>
          {product.salePrice && (
            <span className="text-sm text-gray-500 line-through">
              P{product.salePrice.toFixed(2)}
            </span>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Tabs */}
        <div className="flex flex-wrap justify-center mb-8 border-b">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-6 py-3 font-medium transition-colors ${
                activeTab === tab.id
                  ? 'text-gray-900 border-b-2 border-gray-900'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {tabs.find(tab => tab.id === activeTab)?.products.map((product) => (
            <ProductCard key={product.id} product={product} />
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link
            href="/products"
            className="inline-block bg-gray-900 text-white px-8 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
          >
            View all
          </Link>
        </div>
      </div>
    </section>
  );
}
