import { connectToDbAmieShop } from "@/lib/mongoDb/connectToDbAmieShop";
import { Site } from "@/lib/mongoDb/Models/Site";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{searchParams}) {
    // const filters = await searchParams
    // console.log(filters)
    connectToDbAmieShop()
    try {
        const products= await Product.find()
        return NextResponse.json(products,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get users',{status:501})
    }
}

export async function POST(req) {
    const body=await req.json()
    // console.log(body)
    connectToDbAmieShop()
    try {
        const foundProduct=await Product.findOne({title:body?.title})
        // console.log(foundProduct)
        if(foundProduct)return NextResponse.json('product title exits, enter unique title',{status:501})
        const newProduct=await Product(body)
        newProduct.save()
        return NextResponse.json(newProduct,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to update product details',{status:501})
    }
}

export async function DELETE(request) {
    // console.log(request.json())
    await connectToDbAmieShop()
    try {
        await Product.deleteMany()
        return NextResponse.json('deleted all users from database', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete all users from database", { status: 501 });
    }
}