import { requireAdmin } from '@/middleware/adminAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import AdminDashboard from '@/components/admin/AdminDashboard';
import { Product } from '@/libs/mongoDb/Models/Products';
import { Order } from '@/libs/mongoDb/Models/Order';
import { User } from '@/libs/mongoDb/Models/User';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getDashboardStats() {
  await connectToDbAmieShop();
  
  const [
    totalProducts,
    totalOrders,
    totalCustomers,
    lowStockProducts,
    recentOrders,
    salesData
  ] = await Promise.all([
    Product.countDocuments({ status: 'active' }),
    Order.countDocuments(),
    User.countDocuments({ role: { $ne: 'admin' } }),
    Product.countDocuments({ stock: { $lte: 10 }, status: 'active' }),
    Order.find().sort({ createdAt: -1 }).limit(5).populate('userId', 'name email').lean(),
    Order.aggregate([
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          totalSales: { $sum: '$total' },
          orderCount: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': -1, '_id.month': -1 } },
      { $limit: 12 }
    ])
  ]);

  return {
    totalProducts,
    totalOrders,
    totalCustomers,
    lowStockProducts,
    recentOrders: JSON.parse(JSON.stringify(recentOrders)),
    salesData: JSON.parse(JSON.stringify(salesData))
  };
}

export const metadata = {
  title: 'Admin Dashboard - SkincareAlert',
  description: 'Manage your e-commerce platform',
};

export default async function AdminPage() {
  await requireAdmin();
  const stats = await getDashboardStats();

  return (
    <AdminLayout>
      <AdminDashboard stats={stats} />
    </AdminLayout>
  );
}
