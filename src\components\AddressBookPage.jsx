'use client';

import { useState } from 'react';
import { PlusIcon, PencilIcon, TrashIcon, MapPinIcon, CheckCircleIcon } from 'lucide-react';
import AddressForm from '@/components/AddressForm';

export default function AddressBookPage({ addresses: initialAddresses }) {
  const [addresses, setAddresses] = useState(initialAddresses);
  const [showForm, setShowForm] = useState(false);
  const [editingAddress, setEditingAddress] = useState(null);
  const [loading, setLoading] = useState(false);

  const handleAddAddress = () => {
    setEditingAddress(null);
    setShowForm(true);
  };

  const handleEditAddress = (address) => {
    setEditingAddress(address);
    setShowForm(true);
  };

  const handleDeleteAddress = async (addressId) => {
    if (!confirm('Are you sure you want to delete this address?')) return;

    setLoading(true);
    try {
      const response = await fetch('/api/addresses/delete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ addressId }),
      });

      if (response.ok) {
        setAddresses(prev => prev.filter(addr => addr._id !== addressId));
      } else {
        alert('Failed to delete address');
      }
    } catch (error) {
      console.error('Error deleting address:', error);
      alert('Failed to delete address');
    } finally {
      setLoading(false);
    }
  };

  const handleSetDefault = async (addressId, type) => {
    setLoading(true);
    try {
      const response = await fetch('/api/addresses/set-default', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ addressId, type }),
      });

      if (response.ok) {
        const data = await response.json();
        setAddresses(data.addresses);
      } else {
        alert('Failed to set default address');
      }
    } catch (error) {
      console.error('Error setting default address:', error);
      alert('Failed to set default address');
    } finally {
      setLoading(false);
    }
  };

  const handleFormSuccess = (savedAddress) => {
    if (editingAddress) {
      setAddresses(prev => prev.map(addr => 
        addr._id === savedAddress._id ? savedAddress : addr
      ));
    } else {
      setAddresses(prev => [savedAddress, ...prev]);
    }
    setShowForm(false);
    setEditingAddress(null);
  };

  const getAddressTypeLabel = (type) => {
    switch (type) {
      case 'shipping': return 'Shipping Only';
      case 'billing': return 'Billing Only';
      case 'both': return 'Shipping & Billing';
      default: return type;
    }
  };

  const getAddressTypeColor = (type) => {
    switch (type) {
      case 'shipping': return 'text-blue-600 bg-blue-100';
      case 'billing': return 'text-green-600 bg-green-100';
      case 'both': return 'text-purple-600 bg-purple-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Address Book</h1>
          <p className="text-gray-600">Manage your shipping and billing addresses</p>
        </div>
        <button
          onClick={handleAddAddress}
          className="bg-gray-900 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-800 transition-colors flex items-center space-x-2"
        >
          <PlusIcon className="w-5 h-5" />
          <span>Add Address</span>
        </button>
      </div>

      {/* Address Grid */}
      {addresses.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {addresses.map((address) => (
            <div
              key={address._id}
              className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
            >
              {/* Address Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <MapPinIcon className="w-5 h-5 text-gray-400" />
                  <div>
                    {address.label && (
                      <h3 className="font-semibold text-gray-900">{address.label}</h3>
                    )}
                    <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getAddressTypeColor(address.type)}`}>
                      {getAddressTypeLabel(address.type)}
                    </span>
                  </div>
                </div>
                
                {address.isDefault && (
                  <div className="flex items-center space-x-1 text-green-600">
                    <CheckCircleIcon className="w-4 h-4" />
                    <span className="text-xs font-medium">Default</span>
                  </div>
                )}
              </div>

              {/* Address Details */}
              <div className="space-y-2 mb-4">
                <p className="font-medium text-gray-900">
                  {address.firstName} {address.lastName}
                </p>
                {address.company && (
                  <p className="text-gray-600">{address.company}</p>
                )}
                <div className="text-gray-600">
                  <p>{address.address1}</p>
                  {address.address2 && <p>{address.address2}</p>}
                  <p>{address.city}, {address.state} {address.postalCode}</p>
                  <p>{address.country}</p>
                </div>
                {address.phone && (
                  <p className="text-gray-600">{address.phone}</p>
                )}
                {address.instructions && (
                  <p className="text-sm text-gray-500 italic">
                    Instructions: {address.instructions}
                  </p>
                )}
              </div>

              {/* Address Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleEditAddress(address)}
                    className="text-gray-400 hover:text-gray-600"
                    title="Edit Address"
                  >
                    <PencilIcon className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDeleteAddress(address._id)}
                    disabled={loading}
                    className="text-gray-400 hover:text-red-600 disabled:opacity-50"
                    title="Delete Address"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>

                {!address.isDefault && (
                  <button
                    onClick={() => handleSetDefault(address._id, address.type)}
                    disabled={loading}
                    className="text-sm text-blue-600 hover:text-blue-800 font-medium disabled:opacity-50"
                  >
                    Set as Default
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MapPinIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No addresses yet</h3>
          <p className="text-gray-600 mb-6">
            Add your first address to make checkout faster and easier.
          </p>
          <button
            onClick={handleAddAddress}
            className="bg-gray-900 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
          >
            Add Your First Address
          </button>
        </div>
      )}

      {/* Address Form Modal */}
      {showForm && (
        <AddressForm
          address={editingAddress}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowForm(false);
            setEditingAddress(null);
          }}
        />
      )}
    </div>
  );
}
