import { Geist, <PERSON>eist_Mono } from "next/font/google";
import { SessionProvider } from "next-auth/react";
import CartContextProvider from "@/libs/context/useCartContext";
import Navbar from "@/components/Navbar";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "SkincareAlert - Effective and Affordable Skincare",
  description: "Discover effective and affordable skincare for the everyday person. Shop trusted skincare brands and get expert skin education.",
  keywords: "skincare, beauty, cosmetics, affordable skincare, effective skincare, skin care products",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SessionProvider>
          <CartContextProvider>
            <div className="min-h-screen bg-white">
              <Navbar />
              <main className="w-full">
                {children}
              </main>
            </div>
          </CartContextProvider>
        </SessionProvider>
      </body>
    </html>
  );
}
