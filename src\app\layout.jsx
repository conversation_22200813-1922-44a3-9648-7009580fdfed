import { Gei<PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import CartContextProvider from "@/libs/context/useCartContext";
import Navbar from "@/components/Navbar";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "luyari",
  description: "experince your world",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <div className='flex text-gray-600 flex-col w-full h-full overflow-hidden'>
          <div className='flex flex-col w-full h-fit overflow-y-auto'>
            <CartContextProvider>
              <Navbar/>
              <div className='flex w-full h-fit relative'>
                {children}
              </div>
            </CartContextProvider>
          </div>
        </div>
      </body>
    </html>
  );
}
