'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { 
  XMarkIcon, 
  StarIcon, 
  HeartIcon, 
  ShoppingCartIcon, 
  EyeIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from 'lucide-react';

export default function QuickViewModal({ productId, isOpen, onClose }) {
  const [product, setProduct] = useState(null);
  const [loading, setLoading] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [selectedVariant, setSelectedVariant] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isAddingToWishlist, setIsAddingToWishlist] = useState(false);

  useEffect(() => {
    if (isOpen && productId) {
      fetchProduct();
    }
  }, [isOpen, productId]);

  const fetchProduct = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/products/${productId}`);
      if (response.ok) {
        const data = await response.json();
        setProduct(data.product);
        setSelectedVariant(data.product.variants?.[0] || null);
        setCurrentImageIndex(0);
      }
    } catch (error) {
      console.error('Error fetching product:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = async () => {
    if (!product) return;
    
    setIsAddingToCart(true);
    try {
      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: product._id,
          variantId: selectedVariant?._id,
          quantity,
          price: selectedVariant?.price || product.price,
        }),
      });

      if (response.ok) {
        console.log('Added to cart successfully');
        // Could show a success message here
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleAddToWishlist = async () => {
    if (!product) return;
    
    setIsAddingToWishlist(true);
    try {
      const response = await fetch('/api/wishlist/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ productId: product._id }),
      });

      if (response.ok) {
        console.log('Added to wishlist successfully');
      }
    } catch (error) {
      console.error('Error adding to wishlist:', error);
    } finally {
      setIsAddingToWishlist(false);
    }
  };

  const renderStars = (rating) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIcon
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const nextImage = () => {
    if (product?.images) {
      setCurrentImageIndex((prev) => (prev + 1) % product.images.length);
    }
  };

  const prevImage = () => {
    if (product?.images) {
      setCurrentImageIndex((prev) => (prev - 1 + product.images.length) % product.images.length);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">Quick View</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
          {loading ? (
            <div className="animate-pulse grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="aspect-square bg-gray-200 rounded-lg"></div>
              <div className="space-y-4">
                <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/4"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            </div>
          ) : product ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Product Images */}
              <div className="space-y-4">
                <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
                  {product.images?.[currentImageIndex] ? (
                    <Image
                      src={product.images[currentImageIndex]}
                      alt={product.title}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-gray-400">
                      No image available
                    </div>
                  )}

                  {/* Navigation Arrows */}
                  {product.images?.length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <ChevronLeftIcon className="w-4 h-4 text-gray-700" />
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <ChevronRightIcon className="w-4 h-4 text-gray-700" />
                      </button>
                    </>
                  )}
                </div>

                {/* Thumbnail Grid */}
                {product.images?.length > 1 && (
                  <div className="grid grid-cols-4 gap-2">
                    {product.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`relative aspect-square bg-gray-100 rounded-lg overflow-hidden border-2 transition-colors ${
                          currentImageIndex === index
                            ? 'border-gray-900'
                            : 'border-transparent hover:border-gray-300'
                        }`}
                      >
                        <Image
                          src={image}
                          alt={`${product.title} - ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Info */}
              <div className="space-y-6">
                {/* Brand */}
                <div className="text-sm text-gray-500 uppercase tracking-wide">
                  {product.brand}
                </div>

                {/* Title */}
                <h1 className="text-2xl font-bold text-gray-900">{product.title}</h1>

                {/* Rating */}
                {product.ratingCount > 0 && (
                  <div className="flex items-center space-x-2">
                    {renderStars(product.rating)}
                    <span className="text-sm text-gray-600">
                      ({product.ratingCount} review{product.ratingCount !== 1 ? 's' : ''})
                    </span>
                  </div>
                )}

                {/* Price */}
                <div className="flex items-center space-x-3">
                  <span className="text-2xl font-bold text-gray-900">
                    P{(selectedVariant?.price || product.price).toFixed(2)}
                  </span>
                  {product.salePrice && (
                    <span className="text-lg text-gray-500 line-through">
                      P{product.salePrice.toFixed(2)}
                    </span>
                  )}
                </div>

                {/* Short Description */}
                {product.shortDesc && (
                  <p className="text-gray-700">{product.shortDesc}</p>
                )}

                {/* Variants */}
                {product.variants && product.variants.length > 0 && (
                  <div className="space-y-3">
                    <label className="block text-sm font-medium text-gray-700">
                      Options
                    </label>
                    <div className="flex flex-wrap gap-2">
                      {product.variants.map((variant) => (
                        <button
                          key={variant._id}
                          onClick={() => setSelectedVariant(variant)}
                          className={`px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${
                            selectedVariant?._id === variant._id
                              ? 'border-gray-900 bg-gray-900 text-white'
                              : 'border-gray-300 text-gray-700 hover:border-gray-400'
                          }`}
                        >
                          {variant.size || variant.color || variant.name}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Quantity */}
                <div className="space-y-3">
                  <label className="block text-sm font-medium text-gray-700">
                    Quantity
                  </label>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="w-8 h-8 border border-gray-300 rounded flex items-center justify-center hover:bg-gray-50"
                    >
                      -
                    </button>
                    <span className="w-12 text-center font-medium">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="w-8 h-8 border border-gray-300 rounded flex items-center justify-center hover:bg-gray-50"
                    >
                      +
                    </button>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={handleAddToCart}
                    disabled={!product.inStock || isAddingToCart}
                    className="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                  >
                    <ShoppingCartIcon className="w-5 h-5" />
                    <span>
                      {isAddingToCart ? 'Adding...' : 'Add to Cart'}
                    </span>
                  </button>

                  <div className="flex space-x-3">
                    <button
                      onClick={handleAddToWishlist}
                      disabled={isAddingToWishlist}
                      className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
                    >
                      <HeartIcon className="w-5 h-5" />
                      <span>
                        {isAddingToWishlist ? 'Adding...' : 'Wishlist'}
                      </span>
                    </button>

                    <Link
                      href={`/products/${product.slug}`}
                      className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
                    >
                      <EyeIcon className="w-5 h-5" />
                      <span>View Details</span>
                    </Link>
                  </div>
                </div>

                {/* Key Features */}
                {(product.skinType?.length > 0 || product.skinConcern?.length > 0) && (
                  <div className="border-t pt-6">
                    <h3 className="font-semibold text-gray-900 mb-3">Perfect For</h3>
                    <div className="space-y-2">
                      {product.skinType?.length > 0 && (
                        <div>
                          <span className="text-sm font-medium text-gray-700">Skin Types: </span>
                          <span className="text-sm text-gray-600">{product.skinType.join(', ')}</span>
                        </div>
                      )}
                      {product.skinConcern?.length > 0 && (
                        <div>
                          <span className="text-sm font-medium text-gray-700">Skin Concerns: </span>
                          <span className="text-sm text-gray-600">{product.skinConcern.join(', ')}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">Product not found</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
