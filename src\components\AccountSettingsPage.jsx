'use client';

import { useState } from 'react';
import { UserIcon, LockIcon, BellIcon, SparklesIcon } from 'lucide-react';
import ProfileSettings from '@/components/ProfileSettings';
import PasswordSettings from '@/components/PasswordSettings';
import NotificationSettings from '@/components/NotificationSettings';
import SkinProfileSettings from '@/components/SkinProfileSettings';

const tabs = [
  { id: 'profile', name: 'Profile', icon: UserIcon },
  { id: 'password', name: 'Password', icon: LockIcon },
  { id: 'notifications', name: 'Notifications', icon: BellIcon },
  { id: 'skin-profile', name: 'Skin Profile', icon: SparklesIcon },
];

export default function AccountSettingsPage({ user }) {
  const [activeTab, setActiveTab] = useState('profile');

  const renderTabContent = () => {
    switch (activeTab) {
      case 'profile':
        return <ProfileSettings user={user} />;
      case 'password':
        return <PasswordSettings />;
      case 'notifications':
        return <NotificationSettings user={user} />;
      case 'skin-profile':
        return <SkinProfileSettings user={user} />;
      default:
        return <ProfileSettings user={user} />;
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900">Account Settings</h1>
        <p className="text-gray-600">Manage your account preferences and profile information</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-8">
        <nav className="flex space-x-8">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  isActive
                    ? 'border-gray-900 text-gray-900'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="w-5 h-5" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow-sm">
        {renderTabContent()}
      </div>
    </div>
  );
}
