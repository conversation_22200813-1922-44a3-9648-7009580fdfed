import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Order } from '@/libs/mongoDb/Models/Order';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';
import PDFDocument from 'pdfkit';

export async function GET(request, { params }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { id } = params;

    await connectToDbAmieShop();

    // Find the order and verify ownership
    const order = await Order.findOne({ 
      _id: id, 
      userId: session.user.id 
    }).lean();

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Only allow invoice download for paid orders
    if (order.paymentStatus !== 'paid') {
      return NextResponse.json(
        { error: 'Invoice not available for unpaid orders' },
        { status: 400 }
      );
    }

    // Generate PDF
    const pdfBuffer = await generateInvoicePDF(order);

    // Return PDF as response
    return new NextResponse(pdfBuffer, {
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="invoice-${order.orderNumber}.pdf"`,
        'Content-Length': pdfBuffer.length.toString(),
      },
    });

  } catch (error) {
    console.error('Invoice generation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function generateInvoicePDF(order) {
  return new Promise((resolve, reject) => {
    try {
      const doc = new PDFDocument({ margin: 50 });
      const chunks = [];

      doc.on('data', chunk => chunks.push(chunk));
      doc.on('end', () => resolve(Buffer.concat(chunks)));

      // Company Header
      doc.fontSize(20)
         .text('SkincareAlert', 50, 50)
         .fontSize(10)
         .text('Premium Skincare Products', 50, 75)
         .text('Gaborone, Botswana', 50, 90)
         .text('Email: <EMAIL>', 50, 105)
         .text('Phone: +267 XX XXX XXX', 50, 120);

      // Invoice Title
      doc.fontSize(20)
         .text('INVOICE', 400, 50)
         .fontSize(12)
         .text(`Invoice #: ${order.orderNumber}`, 400, 80)
         .text(`Date: ${new Date(order.createdAt).toLocaleDateString()}`, 400, 100)
         .text(`Due Date: ${new Date(order.createdAt).toLocaleDateString()}`, 400, 120);

      // Customer Information
      doc.fontSize(14)
         .text('Bill To:', 50, 180)
         .fontSize(12)
         .text(`${order.shippingAddress.firstName} ${order.shippingAddress.lastName}`, 50, 200);

      if (order.shippingAddress.company) {
        doc.text(order.shippingAddress.company, 50, 215);
      }

      doc.text(order.shippingAddress.address1, 50, order.shippingAddress.company ? 230 : 215);
      
      if (order.shippingAddress.address2) {
        doc.text(order.shippingAddress.address2, 50, order.shippingAddress.company ? 245 : 230);
      }

      const cityLine = order.shippingAddress.address2 ? 260 : (order.shippingAddress.company ? 245 : 230);
      doc.text(`${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postalCode}`, 50, cityLine);
      doc.text(order.shippingAddress.country, 50, cityLine + 15);

      if (order.shippingAddress.phone) {
        doc.text(`Phone: ${order.shippingAddress.phone}`, 50, cityLine + 30);
      }

      // Order Details
      const tableTop = 320;
      doc.fontSize(14)
         .text('Order Details:', 50, tableTop - 20);

      // Table Headers
      const itemX = 50;
      const qtyX = 300;
      const priceX = 350;
      const totalX = 450;

      doc.fontSize(10)
         .text('Item', itemX, tableTop)
         .text('Qty', qtyX, tableTop)
         .text('Price', priceX, tableTop)
         .text('Total', totalX, tableTop);

      // Draw header line
      doc.moveTo(50, tableTop + 15)
         .lineTo(550, tableTop + 15)
         .stroke();

      // Order Items
      let currentY = tableTop + 25;
      order.items.forEach((item, index) => {
        const itemTotal = item.price * item.quantity;
        
        doc.text(item.title, itemX, currentY, { width: 240 })
           .text(item.quantity.toString(), qtyX, currentY)
           .text(`P${item.price.toFixed(2)}`, priceX, currentY)
           .text(`P${itemTotal.toFixed(2)}`, totalX, currentY);

        if (item.brand) {
          doc.fontSize(8)
             .fillColor('gray')
             .text(`Brand: ${item.brand}`, itemX, currentY + 12)
             .fillColor('black')
             .fontSize(10);
        }

        currentY += 35;
      });

      // Draw line before totals
      currentY += 10;
      doc.moveTo(300, currentY)
         .lineTo(550, currentY)
         .stroke();

      // Totals
      currentY += 20;
      doc.text('Subtotal:', 350, currentY)
         .text(`P${order.subtotal.toFixed(2)}`, totalX, currentY);

      if (order.shippingCost > 0) {
        currentY += 20;
        doc.text('Shipping:', 350, currentY)
           .text(`P${order.shippingCost.toFixed(2)}`, totalX, currentY);
      }

      if (order.taxAmount > 0) {
        currentY += 20;
        doc.text('Tax:', 350, currentY)
           .text(`P${order.taxAmount.toFixed(2)}`, totalX, currentY);
      }

      if (order.discountAmount > 0) {
        currentY += 20;
        doc.text('Discount:', 350, currentY)
           .text(`-P${order.discountAmount.toFixed(2)}`, totalX, currentY);
      }

      // Total
      currentY += 20;
      doc.moveTo(300, currentY)
         .lineTo(550, currentY)
         .stroke();

      currentY += 15;
      doc.fontSize(12)
         .text('Total:', 350, currentY)
         .text(`P${order.total.toFixed(2)}`, totalX, currentY);

      // Payment Information
      currentY += 40;
      doc.fontSize(10)
         .text('Payment Information:', 50, currentY)
         .text(`Payment Method: ${order.paymentMethod}`, 50, currentY + 15)
         .text(`Payment Status: ${order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}`, 50, currentY + 30);

      if (order.stripePaymentIntentId) {
        doc.text(`Transaction ID: ${order.stripePaymentIntentId}`, 50, currentY + 45);
      }

      // Footer
      doc.fontSize(8)
         .fillColor('gray')
         .text('Thank you for your business!', 50, doc.page.height - 100)
         .text('For questions about this invoice, <NAME_EMAIL>', 50, doc.page.height - 85)
         .text('This is a computer-generated invoice and does not require a signature.', 50, doc.page.height - 70);

      doc.end();

    } catch (error) {
      reject(error);
    }
  });
}
