import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import AccountLayout from '@/components/AccountLayout';
import AccountSettingsPage from '@/components/AccountSettingsPage';
import { User } from '@/libs/mongoDb/Models/User';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getUserData(userId) {
  await connectToDbAmieShop();
  
  const user = await User.findById(userId).lean();
  return JSON.parse(JSON.stringify(user));
}

export const metadata = {
  title: 'Account Settings - SkincareAlert',
  description: 'Manage your account preferences and profile',
};

export default async function SettingsPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/account/settings');
  }

  const userData = await getUserData(session.user.id);

  return (
    <AccountLayout>
      <AccountSettingsPage user={userData} />
    </AccountLayout>
  );
}
