import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import stripe from '@/libs/stripe';
import { Cart } from '@/libs/mongoDb/Models/Cart';
import { Product } from '@/libs/mongoDb/Models/Products';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    const { cartId, successUrl, cancelUrl } = await request.json();

    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    await connectToDbAmieShop();

    // Get cart with populated products
    const cart = await Cart.findById(cartId).populate('items.productId');
    
    if (!cart) {
      return NextResponse.json(
        { error: 'Cart not found' },
        { status: 404 }
      );
    }

    // Create line items for Stripe
    const lineItems = await Promise.all(
      cart.items.map(async (item) => {
        const product = await Product.findById(item.productId);
        
        return {
          price_data: {
            currency: 'usd',
            product_data: {
              name: product.title,
              description: product.shortDesc || product.desc.substring(0, 100),
              images: product.images.slice(0, 1), // Stripe allows max 8 images
              metadata: {
                productId: product._id.toString(),
                variantId: item.variantId || '',
              },
            },
            unit_amount: Math.round(item.price * 100), // Convert to cents
          },
          quantity: item.quantity,
        };
      })
    );

    // Create Stripe checkout session
    const checkoutSession = await stripe.checkout.sessions.create({
      payment_method_types: ['card'],
      line_items: lineItems,
      mode: 'payment',
      success_url: successUrl || `${process.env.NEXTAUTH_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: cancelUrl || `${process.env.NEXTAUTH_URL}/cart`,
      customer_email: session.user.email,
      metadata: {
        cartId: cart._id.toString(),
        userId: session.user.id,
      },
      shipping_address_collection: {
        allowed_countries: ['US', 'CA', 'GB', 'AU'], // Add your supported countries
      },
      billing_address_collection: 'required',
      payment_intent_data: {
        metadata: {
          cartId: cart._id.toString(),
          userId: session.user.id,
        },
      },
    });

    return NextResponse.json({
      sessionId: checkoutSession.id,
      url: checkoutSession.url,
    });

  } catch (error) {
    console.error('Checkout error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
