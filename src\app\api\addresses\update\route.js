import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Address } from '@/libs/mongoDb/Models/Address';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { addressId, ...addressData } = await request.json();

    if (!addressId) {
      return NextResponse.json(
        { error: 'Address ID is required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Find the address and verify ownership
    const address = await Address.findOne({ 
      _id: addressId, 
      userId: session.user.id 
    });

    if (!address) {
      return NextResponse.json(
        { error: 'Address not found' },
        { status: 404 }
      );
    }

    // If this is set as default, ensure no other address of the same type is default
    if (addressData.isDefault && !address.isDefault) {
      await Address.updateMany(
        { 
          userId: session.user.id, 
          type: addressData.type,
          _id: { $ne: addressId }
        },
        { isDefault: false }
      );
    }

    // Update the address
    Object.assign(address, addressData);
    await address.save();

    return NextResponse.json({
      message: 'Address updated successfully',
      address
    });

  } catch (error) {
    console.error('Address update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
