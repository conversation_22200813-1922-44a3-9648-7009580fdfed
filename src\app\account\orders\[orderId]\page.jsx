import { redirect, notFound } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import AccountLayout from '@/components/AccountLayout';
import OrderTrackingPage from '@/components/OrderTrackingPage';
import { Order } from '@/libs/mongoDb/Models/Order';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getOrderDetails(orderId, userId) {
  await connectToDbAmieShop();
  
  const order = await Order.findOne({ 
    _id: orderId, 
    userId 
  })
  .populate('items.productId', 'title brand thumbnail slug')
  .lean();

  if (!order) {
    return null;
  }

  return JSON.parse(JSON.stringify(order));
}

export async function generateMetadata({ params }) {
  return {
    title: `Order #${params.orderId} - SkincareAlert`,
    description: 'Track your order status and delivery information',
  };
}

export default async function OrderDetailsPage({ params }) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/account/orders/' + params.orderId);
  }

  const order = await getOrderDetails(params.orderId, session.user.id);

  if (!order) {
    notFound();
  }

  return (
    <AccountLayout>
      <OrderTrackingPage order={order} />
    </AccountLayout>
  );
}
