import { connectToDbAmieShop } from "@/lib/mongoDb/connectToDbAmieShop";
import { Site } from "@/lib/mongoDb/Models/Site";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{params}) {
    const {id}=await params
    // console.log(id)
    connectToDbAmieShop()
    try {
        const user= await User.findById(id,{password:0,__v:0})
        // console.log('server found:-',user)
        return NextResponse.json(user,{status:201})
    } catch (error) {
        return NextResponse.json('failed to find user',{status:501})
    }
}
export async function PUT(req,{params}) {
    const {id}=await params
    const body=await req.json()
    // console.log(body)
    connectToDbAmieShop()
    try {
        await User.findByIdAndUpdate(id,body)
        return NextResponse.json('user details updated',{status:201})
    } catch (error) {
        return NextResponse.json('failed to update user details',{status:501})
    }
}

export async function DELETE(request,{params}) {
    const {id}=await params
    // console.log(id)
    await connectToDbAmieShop()
    try {
        await User.findByIdAndDelete(id)
        return NextResponse.json('user deleted', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete user details", { status: 501 });
    }
}