import mongoose from 'mongoose';
const { Schema } = mongoose;

const giftCardSchema = new Schema({
  // Gift Card Details
  code: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    index: true
  },

  // Value and Balance
  originalValue: { type: Number, required: true, min: 0 },
  currentBalance: { type: Number, required: true, min: 0 },
  currency: { type: String, default: 'BWP' },

  // Status
  status: {
    type: String,
    enum: ['active', 'redeemed', 'expired', 'cancelled'],
    default: 'active'
  },

  // Purchaser Information
  purchasedBy: { type: Schema.Types.ObjectId, ref: 'User' },
  purchaserEmail: { type: String, required: true },
  purchaserName: { type: String },

  // Recipient Information
  recipientEmail: { type: String, required: true },
  recipientName: { type: String },
  personalMessage: { type: String, maxlength: 500 },

  // Delivery
  deliveryMethod: {
    type: String,
    enum: ['email', 'physical', 'digital'],
    default: 'email'
  },
  deliveryDate: { type: Date },
  isDelivered: { type: Boolean, default: false },
  deliveredAt: { type: Date },

  // Expiration
  expiresAt: { type: Date, required: true },
  neverExpires: { type: Boolean, default: false },

  // Usage Tracking
  usageHistory: [{
    orderId: { type: Schema.Types.ObjectId, ref: 'Order' },
    amountUsed: { type: Number, required: true },
    remainingBalance: { type: Number, required: true },
    usedAt: { type: Date, default: Date.now },
    usedBy: { type: Schema.Types.ObjectId, ref: 'User' }
  }],

  // Design and Customization
  design: {
    template: { type: String, default: 'default' },
    backgroundColor: { type: String, default: '#f8f9fa' },
    textColor: { type: String, default: '#333333' },
    customImage: { type: String }, // URL to custom image
  },

  // Restrictions
  restrictions: {
    minimumOrderAmount: { type: Number, default: 0 },
    applicableProducts: [{ type: Schema.Types.ObjectId, ref: 'Product' }],
    applicableCategories: [String],
    excludedProducts: [{ type: Schema.Types.ObjectId, ref: 'Product' }],
    excludedCategories: [String],
    singleUse: { type: Boolean, default: false },
  },

  // Metadata
  source: { type: String, default: 'purchase' }, // purchase, promotion, refund
  notes: { type: String },
  tags: [String],
}, { timestamps: true });

// Indexes for performance
giftCardSchema.index({ code: 1, status: 1 });
giftCardSchema.index({ recipientEmail: 1 });
giftCardSchema.index({ purchaserEmail: 1 });
giftCardSchema.index({ expiresAt: 1 });

// Virtual for checking if gift card is expired
giftCardSchema.virtual('isExpired').get(function() {
  if (this.neverExpires) return false;
  return new Date() > this.expiresAt;
});

// Virtual for checking if gift card is usable
giftCardSchema.virtual('isUsable').get(function() {
  return this.status === 'active' &&
         this.currentBalance > 0 &&
         !this.isExpired;
});

// Method to generate unique gift card code
giftCardSchema.statics.generateCode = function() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let code = 'GC';

  for (let i = 0; i < 12; i++) {
    code += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return code;
};

// Method to create a new gift card
giftCardSchema.statics.createGiftCard = async function(giftCardData) {
  let code;
  let isUnique = false;

  // Generate unique code
  while (!isUnique) {
    code = this.generateCode();
    const existing = await this.findOne({ code });
    if (!existing) {
      isUnique = true;
    }
  }

  // Set expiration date (default 1 year from now)
  const expiresAt = giftCardData.expiresAt ||
    new Date(Date.now() + 365 * 24 * 60 * 60 * 1000);

  const giftCard = new this({
    ...giftCardData,
    code,
    currentBalance: giftCardData.originalValue,
    expiresAt,
    deliveryDate: giftCardData.deliveryDate || new Date()
  });

  await giftCard.save();
  return giftCard;
};

// Method to validate gift card for use
giftCardSchema.methods.validateForUse = function(orderAmount = 0) {
  const errors = [];

  if (this.status !== 'active') {
    errors.push('Gift card is not active');
  }

  if (this.currentBalance <= 0) {
    errors.push('Gift card has no remaining balance');
  }

  if (this.isExpired) {
    errors.push('Gift card has expired');
  }

  if (this.restrictions.minimumOrderAmount > 0 &&
      orderAmount < this.restrictions.minimumOrderAmount) {
    errors.push(`Minimum order amount of P${this.restrictions.minimumOrderAmount.toFixed(2)} required`);
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

// Method to apply gift card to order
giftCardSchema.methods.applyToOrder = function(orderAmount, orderId, userId) {
  const validation = this.validateForUse(orderAmount);

  if (!validation.valid) {
    throw new Error(validation.errors.join(', '));
  }

  // Calculate amount to use
  const amountToUse = Math.min(orderAmount, this.currentBalance);
  const remainingBalance = this.currentBalance - amountToUse;

  // Update balance
  this.currentBalance = remainingBalance;

  // Update status if fully redeemed
  if (remainingBalance <= 0) {
    this.status = 'redeemed';
  }

  // Add to usage history
  this.usageHistory.push({
    orderId,
    amountUsed: amountToUse,
    remainingBalance,
    usedAt: new Date(),
    usedBy: userId
  });

  return {
    amountUsed: amountToUse,
    remainingBalance,
    discountAmount: amountToUse
  };
};

// Static method to find valid gift card by code
giftCardSchema.statics.findValidGiftCard = function(code) {
  return this.findOne({
    code: code.toUpperCase(),
    status: 'active',
    currentBalance: { $gt: 0 },
    $or: [
      { neverExpires: true },
      { expiresAt: { $gt: new Date() } }
    ]
  });
};

// Pre-save middleware
giftCardSchema.pre('save', function(next) {
  // Generate code if not provided
  if (!this.code) {
    this.code = this.constructor.generateCode();
  }

  // Auto-expire if past expiration date
  if (!this.neverExpires && this.expiresAt < new Date() && this.status === 'active') {
    this.status = 'expired';
  }

  // Auto-redeem if balance is zero
  if (this.currentBalance <= 0 && this.status === 'active') {
    this.status = 'redeemed';
  }

  next();
});

export const GiftCard = mongoose.models.GiftCard || mongoose.model('GiftCard', giftCardSchema);
