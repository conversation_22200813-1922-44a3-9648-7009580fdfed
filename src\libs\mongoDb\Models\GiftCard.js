import mongoose from 'mongoose';
const { Schema } = mongoose;

const giftCardSchema = new Schema({
    code: { type: String, required: true, unique: true },
    amount: { type: Number, required: true },
    balance: { type: Number, required: true },
    currency: { type: String, default: 'USD' },
    status: { type: String, enum: ['active', 'used', 'expired', 'cancelled'], default: 'active' },
    purchasedBy: { type: Schema.Types.ObjectId, ref: 'User' },
    purchaseOrderId: { type: Schema.Types.ObjectId, ref: 'Order' },
    recipientEmail: { type: String },
    recipientName: { type: String },
    message: { type: String },
    expiryDate: { type: Date },
    usageHistory: [{
        orderId: { type: Schema.Types.ObjectId, ref: 'Order' },
        amountUsed: { type: Number },
        usedAt: { type: Date, default: Date.now },
    }],
}, { timestamps: true });

// Generate unique gift card code
giftCardSchema.pre('save', function(next) {
    if (!this.code) {
        this.code = 'GC' + Math.random().toString(36).substr(2, 9).toUpperCase();
    }
    next();
});

export const GiftCard = mongoose.models.GiftCard || mongoose.model('GiftCard', giftCardSchema);
