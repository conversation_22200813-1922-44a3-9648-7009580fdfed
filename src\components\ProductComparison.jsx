'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { XMarkIcon, StarIcon, ShoppingCartIcon, HeartIcon } from 'lucide-react';

export default function ProductComparison({ isOpen, onClose, initialProducts = [] }) {
  const [compareProducts, setCompareProducts] = useState(initialProducts);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setCompareProducts(initialProducts);
  }, [initialProducts]);

  const removeProduct = (productId) => {
    setCompareProducts(prev => prev.filter(p => p._id !== productId));
  };

  const addToCart = async (product) => {
    try {
      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: product._id,
          quantity: 1,
          price: product.price,
        }),
      });

      if (response.ok) {
        console.log('Added to cart successfully');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  const addToWishlist = async (product) => {
    try {
      const response = await fetch('/api/wishlist/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ productId: product._id }),
      });

      if (response.ok) {
        console.log('Added to wishlist successfully');
      }
    } catch (error) {
      console.error('Error adding to wishlist:', error);
    }
  };

  const renderStars = (rating) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIcon
            key={star}
            className={`w-4 h-4 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg max-w-7xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900">Product Comparison</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-auto max-h-[calc(90vh-120px)]">
          {compareProducts.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No products to compare</p>
              <button
                onClick={onClose}
                className="bg-gray-900 text-white px-6 py-2 rounded-lg hover:bg-gray-800"
              >
                Close
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr>
                    <td className="p-4 font-medium text-gray-900 w-48">Product</td>
                    {compareProducts.map((product) => (
                      <td key={product._id} className="p-4 text-center min-w-64">
                        <div className="relative">
                          <button
                            onClick={() => removeProduct(product._id)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm hover:bg-red-600"
                          >
                            ×
                          </button>
                          <div className="aspect-square bg-gray-100 rounded-lg mb-3 overflow-hidden">
                            {product.thumbnail || product.images?.[0] ? (
                              <Image
                                src={product.thumbnail || product.images[0]}
                                alt={product.title}
                                width={200}
                                height={200}
                                className="object-cover w-full h-full"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center text-gray-400">
                                No image
                              </div>
                            )}
                          </div>
                          <h3 className="font-medium text-gray-900 text-sm line-clamp-2 mb-2">
                            <Link href={`/products/${product.slug}`} className="hover:text-blue-600">
                              {product.title}
                            </Link>
                          </h3>
                        </div>
                      </td>
                    ))}
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {/* Brand */}
                  <tr>
                    <td className="p-4 font-medium text-gray-700">Brand</td>
                    {compareProducts.map((product) => (
                      <td key={product._id} className="p-4 text-center text-gray-900">
                        {product.brand}
                      </td>
                    ))}
                  </tr>

                  {/* Price */}
                  <tr className="bg-gray-50">
                    <td className="p-4 font-medium text-gray-700">Price</td>
                    {compareProducts.map((product) => (
                      <td key={product._id} className="p-4 text-center">
                        <div className="space-y-1">
                          <span className="font-semibold text-gray-900">
                            P{product.price.toFixed(2)}
                          </span>
                          {product.salePrice && (
                            <div className="text-sm text-gray-500 line-through">
                              P{product.salePrice.toFixed(2)}
                            </div>
                          )}
                        </div>
                      </td>
                    ))}
                  </tr>

                  {/* Rating */}
                  <tr>
                    <td className="p-4 font-medium text-gray-700">Rating</td>
                    {compareProducts.map((product) => (
                      <td key={product._id} className="p-4 text-center">
                        <div className="flex flex-col items-center space-y-1">
                          {renderStars(product.rating)}
                          <span className="text-sm text-gray-600">
                            ({product.ratingCount} reviews)
                          </span>
                        </div>
                      </td>
                    ))}
                  </tr>

                  {/* Stock Status */}
                  <tr className="bg-gray-50">
                    <td className="p-4 font-medium text-gray-700">Availability</td>
                    {compareProducts.map((product) => (
                      <td key={product._id} className="p-4 text-center">
                        <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${
                          product.inStock 
                            ? 'text-green-600 bg-green-100' 
                            : 'text-red-600 bg-red-100'
                        }`}>
                          {product.inStock ? 'In Stock' : 'Out of Stock'}
                        </span>
                      </td>
                    ))}
                  </tr>

                  {/* Skin Type */}
                  {compareProducts.some(p => p.skinType?.length > 0) && (
                    <tr>
                      <td className="p-4 font-medium text-gray-700">Skin Type</td>
                      {compareProducts.map((product) => (
                        <td key={product._id} className="p-4 text-center text-sm text-gray-600">
                          {product.skinType?.join(', ') || 'N/A'}
                        </td>
                      ))}
                    </tr>
                  )}

                  {/* Skin Concerns */}
                  {compareProducts.some(p => p.skinConcern?.length > 0) && (
                    <tr className="bg-gray-50">
                      <td className="p-4 font-medium text-gray-700">Skin Concerns</td>
                      {compareProducts.map((product) => (
                        <td key={product._id} className="p-4 text-center text-sm text-gray-600">
                          {product.skinConcern?.join(', ') || 'N/A'}
                        </td>
                      ))}
                    </tr>
                  )}

                  {/* Weight */}
                  {compareProducts.some(p => p.weight) && (
                    <tr>
                      <td className="p-4 font-medium text-gray-700">Weight/Size</td>
                      {compareProducts.map((product) => (
                        <td key={product._id} className="p-4 text-center text-sm text-gray-600">
                          {product.weight || 'N/A'}
                        </td>
                      ))}
                    </tr>
                  )}

                  {/* Key Ingredients */}
                  {compareProducts.some(p => p.ingredients) && (
                    <tr className="bg-gray-50">
                      <td className="p-4 font-medium text-gray-700">Key Ingredients</td>
                      {compareProducts.map((product) => (
                        <td key={product._id} className="p-4 text-center text-sm text-gray-600">
                          <div className="max-h-20 overflow-y-auto">
                            {product.ingredients ? 
                              product.ingredients.substring(0, 100) + (product.ingredients.length > 100 ? '...' : '')
                              : 'N/A'
                            }
                          </div>
                        </td>
                      ))}
                    </tr>
                  )}

                  {/* Actions */}
                  <tr>
                    <td className="p-4 font-medium text-gray-700">Actions</td>
                    {compareProducts.map((product) => (
                      <td key={product._id} className="p-4 text-center">
                        <div className="space-y-2">
                          <button
                            onClick={() => addToCart(product)}
                            disabled={!product.inStock}
                            className="w-full bg-gray-900 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                          >
                            <ShoppingCartIcon className="w-4 h-4" />
                            <span>Add to Cart</span>
                          </button>
                          <button
                            onClick={() => addToWishlist(product)}
                            className="w-full border border-gray-300 text-gray-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
                          >
                            <HeartIcon className="w-4 h-4" />
                            <span>Wishlist</span>
                          </button>
                        </div>
                      </td>
                    ))}
                  </tr>
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
