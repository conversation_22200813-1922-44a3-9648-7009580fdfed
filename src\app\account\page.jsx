import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import AccountDashboard from '@/components/AccountDashboard';
import AccountLayout from '@/components/AccountLayout';

export const metadata = {
  title: 'My Account - SkincareAlert',
  description: 'Manage your account, orders, and preferences',
};

export default async function AccountPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/account');
  }

  return (
    <AccountLayout>
      <AccountDashboard user={session.user} />
    </AccountLayout>
  );
}
