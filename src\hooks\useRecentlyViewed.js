'use client';

import { useState, useEffect } from 'react';

const STORAGE_KEY = 'recentlyViewedProducts';
const MAX_ITEMS = 10;

export function useRecentlyViewed() {
  const [recentlyViewed, setRecentlyViewed] = useState([]);

  useEffect(() => {
    // Load from localStorage on mount
    if (typeof window !== 'undefined') {
      try {
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          setRecentlyViewed(JSON.parse(stored));
        }
      } catch (error) {
        console.error('Error loading recently viewed products:', error);
      }
    }
  }, []);

  const addProduct = (product) => {
    if (!product || !product._id) return;

    const productData = {
      _id: product._id,
      title: product.title,
      brand: product.brand,
      price: product.price,
      salePrice: product.salePrice,
      thumbnail: product.thumbnail || product.images?.[0],
      slug: product.slug,
      rating: product.rating,
      ratingCount: product.ratingCount,
      inStock: product.inStock,
      viewedAt: new Date().toISOString(),
    };

    setRecentlyViewed(prev => {
      // Remove if already exists
      const filtered = prev.filter(item => item._id !== product._id);
      
      // Add to beginning
      const updated = [productData, ...filtered];
      
      // Limit to MAX_ITEMS
      const limited = updated.slice(0, MAX_ITEMS);
      
      // Save to localStorage
      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(limited));
        } catch (error) {
          console.error('Error saving recently viewed products:', error);
        }
      }
      
      return limited;
    });
  };

  const removeProduct = (productId) => {
    setRecentlyViewed(prev => {
      const updated = prev.filter(item => item._id !== productId);
      
      // Save to localStorage
      if (typeof window !== 'undefined') {
        try {
          localStorage.setItem(STORAGE_KEY, JSON.stringify(updated));
        } catch (error) {
          console.error('Error saving recently viewed products:', error);
        }
      }
      
      return updated;
    });
  };

  const clearAll = () => {
    setRecentlyViewed([]);
    
    if (typeof window !== 'undefined') {
      try {
        localStorage.removeItem(STORAGE_KEY);
      } catch (error) {
        console.error('Error clearing recently viewed products:', error);
      }
    }
  };

  return {
    recentlyViewed,
    addProduct,
    removeProduct,
    clearAll,
  };
}
