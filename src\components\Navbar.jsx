import Link from 'next/link'
import React from 'react'
import Notifications from './Notifications'
import SearchComponent from './SearchComponent'
import UserSettings from './UserSettings'
import BtnLinkComponent from './BtnLinkComponent'
import BtnWishlistComponent from './BtnWishlistComponent'

export default async function Navbar({data,params}) {
  // const session=true
  return (
    <div className='flex relative z-50 flex-col w-full justify-end h-fit'>
      <Notifications/>
      <div className='flex relative items-center justify-center w-full md:h-36 h-20 md:px-16 px-2'>
        <div className='flex relative w-1/3 items-start justify-start'>
            <SearchComponent/>
        </div>
        <Link href={'/amieshop'} className='flex w-1/3 text-center capitalize items-center justify-center md:text-4xl text-2xl flex-none'>
            {data?.storeName}
        </Link>
        <div className='flex relative w-1/3 items-center justify-end md:gap-5 gap-2'>
            <BtnWishlistComponent />
            <UserSettings/>
        </div>
      </div>
      <div className='md:flex hidden  relative gap-8 items-center px-32 h-14 justify-center'>
        <BtnLinkComponent data={data}/>
      </div>
    </div>
  )
}
