'use client';

import { useState } from 'react';
import Link from 'next/link';
import { useSession, signIn, signOut } from 'next-auth/react';
import {
  Search,
  ShoppingCart,
  Heart,
  User,
  Menu,
  X,
  ChevronDown
} from 'lucide-react';
import SearchComponent from './SearchComponent';
import CartComponent from './CartComponent';
import WishlistComponent from './WishlistComponent';

const navigationItems = [
  {
    name: 'Home',
    href: '/',
  },
  {
    name: 'Brands',
    href: '/brands',
    submenu: [
      { name: 'Fundamentals', href: '/brands/fundamentals-skincare' },
      { name: 'Lanolab', href: '/brands/lanolab' },
      { name: 'Moritelo', href: '/brands/moritelo' },
      { name: 'Nakolwethu', href: '/brands/nakolwethu' },
      { name: 'Organic Naturals', href: '/brands/organic-naturals' },
      { name: 'Perile', href: '/brands/perile' },
      { name: 'Silki', href: '/brands/silki' },
      { name: 'Skinbliss', href: '/brands/skinbliss' },
      { name: 'SKIN Functional', href: '/brands/skin-functional' },
      { name: 'Skinstem', href: '/brands/skinstem' },
      { name: 'Standard Beauty', href: '/brands/standard-beauty' },
      { name: 'Your Only One', href: '/brands/your-only-one' },
    ]
  },
  {
    name: 'Face',
    href: '/collections/face',
    submenu: [
      {
        category: 'By Product Type',
        items: [
          { name: 'Cleansers', href: '/collections/cleansers' },
          { name: 'Masks & Scrubs', href: '/collections/masks' },
          { name: 'Toners, Essences & Mists', href: '/collections/toners' },
          { name: 'Exfoliators', href: '/collections/exfoliators' },
          { name: 'Serums', href: '/collections/serums' },
          { name: 'Moisturizers', href: '/collections/moisturizers' },
          { name: 'Lip Care', href: '/collections/lip-care' },
          { name: 'Eye Care', href: '/collections/eye-care' },
          { name: 'Sun protection', href: '/collections/sun-protection' },
          { name: 'Tools', href: '/collections/facial-tools' },
        ]
      },
      {
        category: 'By Skin Concern',
        items: [
          { name: 'Acne & Blemishes', href: '/collections/acne-blemishes' },
          { name: 'Combination Skin', href: '/collections/combination-skin' },
          { name: 'Dark Circles & Puffiness', href: '/collections/dark-circles' },
          { name: 'Dehydrated Skin', href: '/collections/dehydrated-skin' },
          { name: 'Dry Skin', href: '/collections/dry-skin' },
          { name: 'Dull Skin', href: '/collections/dull-skin' },
          { name: 'Fine Lines & Wrinkles', href: '/collections/anti-aging' },
          { name: 'Hyperpigmentation & Sun Damage', href: '/collections/hyperpigmentation' },
          { name: 'Inflammation & Rosacea', href: '/collections/sensitive-skin' },
          { name: 'Oily Skin', href: '/collections/oily-skin' },
          { name: 'Sensitive Skin', href: '/collections/sensitive-skin' },
          { name: 'Skin Barrier Repair', href: '/collections/barrier-repair' },
          { name: 'Visible Pores & Texture', href: '/collections/pores-texture' },
        ]
      }
    ]
  },
  {
    name: 'Body',
    href: '/collections/body',
    submenu: [
      {
        category: 'By Product Type',
        items: [
          { name: 'Cleansers', href: '/collections/body-wash' },
          { name: 'Shaving Butters, Creams & Hair Removal', href: '/collections/shaving' },
          { name: 'Scrubs, Masks & Polishes', href: '/collections/body-scrubs' },
          { name: 'Lotions, Creams & Butters', href: '/collections/body-lotions' },
          { name: 'Oils', href: '/collections/body-oils' },
          { name: 'Personal Care', href: '/collections/personal-care' },
          { name: 'Tools', href: '/collections/body-tools' },
        ]
      },
      {
        category: 'By Skin Concern',
        items: [
          { name: 'Body Acne', href: '/collections/body-acne' },
          { name: 'Dry & Aging Skin', href: '/collections/dry-aging-skin' },
          { name: 'Firming & Stretchmarks', href: '/collections/firming-stretchmarks' },
          { name: 'Texture & Keratosis Pilaris', href: '/collections/texture-kp' },
          { name: 'Uneven Skin Tone & Marks', href: '/collections/uneven-tone' },
        ]
      }
    ]
  },
  {
    name: 'Gift Card',
    href: '/gift-cards',
  },
  {
    name: 'About Us',
    href: '/about',
  },
  {
    name: 'Contact Us',
    href: '/contact',
  },
  {
    name: 'Blog',
    href: '/blog',
  }
];

export default function Navbar() {
  const { data: session } = useSession();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const [isWishlistOpen, setIsWishlistOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);

  return (
    <nav className="bg-white shadow-sm sticky top-0 z-50">
      {/* Top Bar */}
      <div className="bg-gray-900 text-white text-center py-2 text-sm">
        Free delivery for order of P700 and above. Nationwide delivery.
      </div>

      {/* Main Navigation */}
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <div className="text-2xl font-bold text-gray-900">
              SkincareAlert
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div
                key={item.name}
                className="relative group"
                onMouseEnter={() => setActiveDropdown(item.name)}
                onMouseLeave={() => setActiveDropdown(null)}
              >
                <Link
                  href={item.href}
                  className="text-gray-700 hover:text-gray-900 px-3 py-2 text-sm font-medium flex items-center"
                >
                  {item.name}
                  {item.submenu && (
                    <ChevronDown className="ml-1 w-4 h-4" />
                  )}
                </Link>

                {/* Dropdown Menu */}
                {item.submenu && activeDropdown === item.name && (
                  <div className="absolute top-full left-0 mt-1 w-96 bg-white shadow-lg rounded-lg border z-50">
                    <div className="p-4">
                      {item.submenu.map((submenuItem, index) => (
                        <div key={index}>
                          {submenuItem.category ? (
                            <div className="mb-4">
                              <h3 className="font-semibold text-gray-900 mb-2">
                                {submenuItem.category}
                              </h3>
                              <div className="grid grid-cols-2 gap-2">
                                {submenuItem.items.map((subItem) => (
                                  <Link
                                    key={subItem.name}
                                    href={subItem.href}
                                    className="text-sm text-gray-600 hover:text-gray-900 py-1"
                                  >
                                    {subItem.name}
                                  </Link>
                                ))}
                              </div>
                            </div>
                          ) : (
                            <Link
                              href={submenuItem.href}
                              className="block text-sm text-gray-600 hover:text-gray-900 py-1"
                            >
                              {submenuItem.name}
                            </Link>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Right Side Icons */}
          <div className="flex items-center space-x-4">
            {/* Search */}
            <button
              onClick={() => setIsSearchOpen(true)}
              className="text-gray-700 hover:text-gray-900"
            >
              <Search className="w-6 h-6" />
            </button>

            {/* User Account */}
            <div className="relative group">
              <button className="text-gray-700 hover:text-gray-900">
                <User className="w-6 h-6" />
              </button>

              {/* User Dropdown */}
              <div className="absolute right-0 top-full mt-1 w-48 bg-white shadow-lg rounded-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                <div className="p-2">
                  {session ? (
                    <>
                      <div className="px-3 py-2 text-sm text-gray-700 border-b">
                        {session.user.email}
                      </div>
                      <Link
                        href="/account"
                        className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                      >
                        My Account
                      </Link>
                      <Link
                        href="/orders"
                        className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                      >
                        Orders
                      </Link>
                      <button
                        onClick={() => signOut()}
                        className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                      >
                        Sign Out
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={() => signIn()}
                        className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                      >
                        Sign In
                      </button>
                      <Link
                        href="/auth/register"
                        className="block px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded"
                      >
                        Register
                      </Link>
                    </>
                  )}
                </div>
              </div>
            </div>

            {/* Wishlist */}
            <button
              onClick={() => setIsWishlistOpen(true)}
              className="text-gray-700 hover:text-gray-900 relative"
            >
              <Heart className="w-6 h-6" />
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </button>

            {/* Cart */}
            <button
              onClick={() => setIsCartOpen(true)}
              className="text-gray-700 hover:text-gray-900 relative"
            >
              <ShoppingCart className="w-6 h-6" />
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                0
              </span>
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden text-gray-700 hover:text-gray-900"
            >
              {isMobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-200">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Search Modal */}
      {isSearchOpen && (
        <SearchComponent onClose={() => setIsSearchOpen(false)} />
      )}

      {/* Cart Sidebar */}
      {isCartOpen && (
        <CartComponent onClose={() => setIsCartOpen(false)} />
      )}

      {/* Wishlist Sidebar */}
      {isWishlistOpen && (
        <WishlistComponent onClose={() => setIsWishlistOpen(false)} />
      )}
    </nav>
  );
}
