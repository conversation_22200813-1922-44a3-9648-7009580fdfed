'use client';

import { useRecentlyViewed } from '@/hooks/useRecentlyViewed';
import Link from 'next/link';
import Image from 'next/image';
import { StarIcon, XMarkIcon, ShoppingCartIcon, HeartIcon } from 'lucide-react';

export default function RecentlyViewed({ className = '' }) {
  const { recentlyViewed, removeProduct } = useRecentlyViewed();

  const renderStars = (rating, count) => {
    return (
      <div className="flex items-center space-x-1">
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <StarIcon
              key={star}
              className={`w-3 h-3 ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
              }`}
            />
          ))}
        </div>
        {count > 0 && (
          <span className="text-xs text-gray-600">({count})</span>
        )}
      </div>
    );
  };

  const handleAddToCart = async (product) => {
    try {
      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: product._id,
          quantity: 1,
          price: product.price,
        }),
      });

      if (response.ok) {
        console.log('Added to cart successfully');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  const handleAddToWishlist = async (product) => {
    try {
      const response = await fetch('/api/wishlist/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ productId: product._id }),
      });

      if (response.ok) {
        console.log('Added to wishlist successfully');
      }
    } catch (error) {
      console.error('Error adding to wishlist:', error);
    }
  };

  if (recentlyViewed.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Recently Viewed</h3>
        <span className="text-sm text-gray-500">
          {recentlyViewed.length} item{recentlyViewed.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {recentlyViewed.slice(0, 8).map((product) => (
          <div
            key={product._id}
            className="group relative bg-gray-50 rounded-lg overflow-hidden hover:shadow-md transition-all duration-300"
          >
            {/* Remove Button */}
            <button
              onClick={() => removeProduct(product._id)}
              className="absolute top-2 right-2 z-10 bg-white/80 hover:bg-white p-1 rounded-full shadow-sm opacity-0 group-hover:opacity-100 transition-opacity"
            >
              <XMarkIcon className="w-4 h-4 text-gray-600" />
            </button>

            {/* Product Image */}
            <div className="relative aspect-square bg-gray-100">
              <Link href={`/products/${product.slug}`}>
                {product.thumbnail ? (
                  <Image
                    src={product.thumbnail}
                    alt={product.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                    No image
                  </div>
                )}
              </Link>

              {/* Hover Actions */}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
                <button
                  onClick={() => handleAddToWishlist(product)}
                  className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <HeartIcon className="w-4 h-4 text-gray-700" />
                </button>
                <button
                  onClick={() => handleAddToCart(product)}
                  disabled={!product.inStock}
                  className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ShoppingCartIcon className="w-4 h-4 text-gray-700" />
                </button>
              </div>
            </div>

            {/* Product Info */}
            <div className="p-3 space-y-1">
              <div className="text-xs text-gray-500">{product.brand}</div>
              <h4 className="font-medium text-gray-900 text-sm line-clamp-2">
                <Link 
                  href={`/products/${product.slug}`}
                  className="hover:text-blue-600 transition-colors"
                >
                  {product.title}
                </Link>
              </h4>
              
              {/* Rating */}
              {product.ratingCount > 0 && (
                <div className="py-1">
                  {renderStars(product.rating, product.ratingCount)}
                </div>
              )}
              
              {/* Price */}
              <div className="flex items-center space-x-2">
                <span className="font-semibold text-gray-900 text-sm">
                  P{product.price.toFixed(2)}
                </span>
                {product.salePrice && (
                  <span className="text-xs text-gray-500 line-through">
                    P{product.salePrice.toFixed(2)}
                  </span>
                )}
              </div>

              {/* Stock Status */}
              <div className="text-xs">
                {product.inStock ? (
                  <span className="text-green-600">In Stock</span>
                ) : (
                  <span className="text-red-600">Out of Stock</span>
                )}
              </div>

              {/* Viewed Time */}
              <div className="text-xs text-gray-400 pt-1">
                Viewed {new Date(product.viewedAt).toLocaleDateString()}
              </div>
            </div>
          </div>
        ))}
      </div>

      {recentlyViewed.length > 8 && (
        <div className="mt-4 text-center">
          <Link
            href="/account/recently-viewed"
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            View All Recently Viewed →
          </Link>
        </div>
      )}
    </div>
  );
}
