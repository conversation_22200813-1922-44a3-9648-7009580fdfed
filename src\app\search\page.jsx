import { Suspense } from 'react';
import AdvancedSearch from '@/components/AdvancedSearch';
import { Brand } from '@/libs/mongoDb/Models/Brand';
import { Category } from '@/libs/mongoDb/Models/Category';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getFilters() {
  await connectToDbAmieShop();
  
  const [brands, categories] = await Promise.all([
    Brand.find({ status: 'active' }).sort({ name: 1 }).lean(),
    Category.find({ status: 'active' }).sort({ name: 1 }).lean(),
  ]);

  return {
    brands: JSON.parse(JSON.stringify(brands)),
    categories: JSON.parse(JSON.stringify(categories)),
  };
}

export const metadata = {
  title: 'Search Products - SkincareAlert',
  description: 'Find the perfect skincare products for your needs',
};

export default async function SearchPage() {
  const { brands, categories } = await getFilters();

  return (
    <div className="min-h-screen bg-gray-50">
      <Suspense fallback={
        <div className="container mx-auto px-4 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-gray-200 rounded w-1/4"></div>
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="h-96 bg-gray-200 rounded"></div>
              <div className="lg:col-span-3 space-y-4">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i} className="h-32 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      }>
        <AdvancedSearch brands={brands} categories={categories} />
      </Suspense>
    </div>
  );
}
