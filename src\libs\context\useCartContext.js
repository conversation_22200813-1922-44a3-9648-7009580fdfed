'use client'
import React, { createContext, useContext, useReducer, useState } from 'react'
import { INITIAL_CART_STATE, reducerCartContext } from './reducerCart'

export const CartContext=createContext()

export default function CartContextProvider({children}) {

    const [showCart,setshowCart]=useState(false)
    const [showWishList,setshowWishList]=useState(false)
    const [cartState,cartDispatch]=useReducer(reducerCartContext,INITIAL_CART_STATE)

  return (
    <CartContext.Provider
        value={{showCart,setshowCart,cartState,cartDispatch,showWishList,setshowWishList}}
    >
      {children}
    </CartContext.Provider>
  )
}

export const  useCartContext=()=>{
    return useContext(CartContext)
}

