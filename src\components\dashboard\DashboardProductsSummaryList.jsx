import React from 'react'
import DashboardProductSummary from './DashboardProductSummary'
import Link from 'next/link'

export default function DashboardProductsSummaryList({data}) {
  // console.log('DashboardProductsSummaryList:-',data[0])
  return (
    <div className='flex flex-col gap-2 w-full h-fit rounded-lg bg-white shadow-lg p-2'>
      <span className='font-medium text-3xl'>Product list</span>
      <div className='flex flex-col gap-2 w-full h-fit'>
        <div className='flex w-full uppercase text-xs gap-2 mb-2'>
          <div className='flex w-full uppercase text-xs gap-2'>
            <div className='flex flex-[1] items-center justify-center'>product image</div>
            <div className='hidden md:flex flex-[1] items-center justify-center'>id</div>
            <div className='flex flex-[1] items-center justify-center'>Product</div>
            <div className='flex flex-[1] items-center justify-center'>brand</div>
            <div className='flex flex-[1] items-center justify-center'>Price</div>
            <div className='flex flex-[1] items-center justify-center'>inStock</div>
            <div className='flex flex-[1] items-center justify-center'>date registered</div>
          </div>
          <div className='flex flex-[.5] h-full items-center justify-center'>
            actions
          </div>
        </div>
        <div className='flex flex-col gap-2'>
          {data.splice(0,5).map((item,index)=>
            <DashboardProductSummary key={index} data={item}/>
          )}
        </div>
      </div>
    </div>
  )
}
