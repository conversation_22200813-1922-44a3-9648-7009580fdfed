import mongoose from 'mongoose';
const { Schema } = mongoose;

const categorySchema = new Schema({
    name: { type: String, required: true, unique: true },
    slug: { type: String, unique: true },
    description: { type: String },
    image: { type: String },
    parent: { type: Schema.Types.ObjectId, ref: 'Category' },
    level: { type: Number, default: 0 }, // 0 for main categories, 1 for subcategories, etc.
    featured: { type: Boolean, default: false },
    status: { type: String, enum: ['active', 'inactive'], default: 'active' },
    seoTitle: { type: String },
    seoDescription: { type: String },
    productCount: { type: Number, default: 0 },
    sortOrder: { type: Number, default: 0 },
}, { timestamps: true });

// Create slug from name before saving
categorySchema.pre('save', function(next) {
    if (this.isModified('name') && !this.slug) {
        this.slug = this.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    }
    next();
});

export const Category = mongoose.models.Category || mongoose.model('Category', categorySchema);
