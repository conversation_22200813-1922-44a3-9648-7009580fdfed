import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { GiftCard } from '@/libs/mongoDb/Models/GiftCard';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';
import { sendEmail } from '@/libs/email/emailService';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    const {
      amount,
      recipientEmail,
      recipientName,
      personalMessage,
      deliveryDate,
      design,
      paymentMethodId
    } = await request.json();

    // Validation
    if (!amount || amount < 10 || amount > 1000) {
      return NextResponse.json(
        { error: 'Gift card amount must be between P10 and P1000' },
        { status: 400 }
      );
    }

    if (!recipientEmail) {
      return NextResponse.json(
        { error: 'Recipient email is required' },
        { status: 400 }
      );
    }

    if (!paymentMethodId) {
      return NextResponse.json(
        { error: 'Payment method is required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Create Stripe payment intent
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(amount * 100), // Convert to cents
      currency: 'bwp',
      payment_method: paymentMethodId,
      confirm: true,
      return_url: `${process.env.NEXTAUTH_URL}/gift-cards/success`,
      metadata: {
        type: 'gift_card_purchase',
        amount: amount.toString(),
        recipientEmail,
        purchaserEmail: session?.user?.email || 'guest'
      }
    });

    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json(
        { error: 'Payment failed' },
        { status: 400 }
      );
    }

    // Create gift card
    const giftCardData = {
      originalValue: amount,
      purchasedBy: session?.user?.id,
      purchaserEmail: session?.user?.email || 'guest',
      purchaserName: session?.user?.name || 'Guest',
      recipientEmail,
      recipientName,
      personalMessage,
      deliveryDate: deliveryDate ? new Date(deliveryDate) : new Date(),
      design: {
        template: design?.template || 'default',
        backgroundColor: design?.backgroundColor || '#f8f9fa',
        textColor: design?.textColor || '#333333',
        customImage: design?.customImage
      },
      source: 'purchase',
      tags: ['purchased']
    };

    const giftCard = await GiftCard.createGiftCard(giftCardData);

    // Send gift card email to recipient
    try {
      await sendGiftCardEmail(giftCard);
    } catch (emailError) {
      console.error('Gift card email error:', emailError);
      // Don't fail the purchase if email fails
    }

    // Send purchase confirmation to buyer
    if (session?.user?.email) {
      try {
        await sendPurchaseConfirmationEmail(giftCard, session.user);
      } catch (emailError) {
        console.error('Purchase confirmation email error:', emailError);
      }
    }

    return NextResponse.json({
      message: 'Gift card purchased successfully',
      giftCard: {
        id: giftCard._id,
        code: giftCard.code,
        amount: giftCard.originalValue,
        recipientEmail: giftCard.recipientEmail,
        deliveryDate: giftCard.deliveryDate
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Gift card purchase error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function sendGiftCardEmail(giftCard) {
  const emailContent = {
    subject: `You've received a SkincareAlert Gift Card! 🎁`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: ${giftCard.design.backgroundColor};">
        <div style="padding: 40px 20px; text-align: center;">
          <h1 style="color: ${giftCard.design.textColor}; margin: 0 0 20px 0;">You've Got a Gift! 🎁</h1>
          
          <div style="background-color: white; border-radius: 12px; padding: 30px; margin: 20px 0; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">SkincareAlert Gift Card</h2>
            
            ${giftCard.personalMessage ? `
              <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 20px 0; font-style: italic;">
                "${giftCard.personalMessage}"
              </div>
            ` : ''}
            
            <div style="background-color: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #1976d2;">Gift Card Details</h3>
              <p><strong>Code:</strong> <span style="font-family: monospace; font-size: 18px; background-color: #fff; padding: 5px 10px; border-radius: 4px;">${giftCard.code}</span></p>
              <p><strong>Value:</strong> P${giftCard.originalValue.toFixed(2)}</p>
              <p><strong>Expires:</strong> ${giftCard.neverExpires ? 'Never' : new Date(giftCard.expiresAt).toLocaleDateString()}</p>
            </div>
            
            <div style="margin: 30px 0;">
              <a href="${process.env.NEXTAUTH_URL}/products" 
                 style="background-color: #333; color: white; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                Start Shopping
              </a>
            </div>
            
            <p style="color: #666; font-size: 14px; margin-top: 30px;">
              From: ${giftCard.purchaserName || 'A friend'}<br>
              To redeem, enter the code at checkout or 
              <a href="${process.env.NEXTAUTH_URL}/gift-cards/redeem?code=${giftCard.code}">click here</a>
            </p>
          </div>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
          <p>Thank you for choosing SkincareAlert!</p>
          <p>Gaborone, Botswana | <EMAIL></p>
        </div>
      </div>
    `
  };

  await sendEmail(giftCard.recipientEmail, 'newsletter', {
    user: { email: giftCard.recipientEmail, name: giftCard.recipientName },
    content: emailContent
  });
}

async function sendPurchaseConfirmationEmail(giftCard, purchaser) {
  const emailContent = {
    subject: `Gift Card Purchase Confirmation - ${giftCard.code}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">SkincareAlert</h1>
          <p style="color: #666; margin: 5px 0;">Gift Card Purchase Confirmation</p>
        </div>
        
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Thank you for your gift card purchase!</h2>
          <p>Hi ${purchaser.name},</p>
          <p>Your gift card has been successfully purchased and will be delivered to the recipient.</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Purchase Details</h3>
            <p><strong>Gift Card Code:</strong> ${giftCard.code}</p>
            <p><strong>Value:</strong> P${giftCard.originalValue.toFixed(2)}</p>
            <p><strong>Recipient:</strong> ${giftCard.recipientEmail}</p>
            <p><strong>Delivery Date:</strong> ${new Date(giftCard.deliveryDate).toLocaleDateString()}</p>
          </div>
          
          <p>The gift card has been sent to <strong>${giftCard.recipientEmail}</strong> and they can start using it immediately.</p>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
          <p>Thank you for spreading the joy of skincare!</p>
          <p>Gaborone, Botswana | <EMAIL></p>
        </div>
      </div>
    `
  };

  await sendEmail(purchaser.email, 'newsletter', {
    user: purchaser,
    content: emailContent
  });
}
