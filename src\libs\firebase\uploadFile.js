import { ref, uploadBytesResumable, getDownloadURL } from "firebase/storage";
import { storage } from "./firebase";

const upload = (file,path,setProgress,setPending,setInfoFields) => {
    setPending(false)
    
    console.log(file,path)
    
    const storageRef = ref(storage, path);
    
    const uploadTask = uploadBytesResumable(storageRef, file);

    try {
        // Register three observers:
        // 1. 'state_changed' observer, called any time the state changes
        // 2. <PERSON><PERSON>r observer, called on failure
        // 3. Completion observer, called on successful completion
        uploadTask.on('state_changed', 
          (snapshot) => {
            // Observe state change events such as progress, pause, and resume
            // Get task progress, including the number of bytes uploaded and the total number of bytes to be uploaded
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            setProgress(progress)
            console.log(progress)
            // console.log('Upload is ' + progress + '% done');
            switch (snapshot.state) {
              case 'paused':
                setPending(false)
                // console.log('Upload is paused');
                break;
              case 'running':
                // console.log('Upload is running');
                setPending(true)
                break;
            }
          }, 
          (error) => {
            return error
            // Handle unsuccessful uploads
          }, 
          () => {
            // Handle successful uploads on complete
            // For instance, get the download URL: https://firebasestorage.googleapis.com/...
            getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
            //   console.log('File available at', downloadURL);
            setInfoFields(prev=>prev={...prev,image:{profileImage:downloadURL,path:path}})
              // fileUrl=downloadURL
            });
            setPending(false)
          }
        );
        // setFileUrl(fileUrl)
    } catch (error) {
        console.log(error)
    }
}

const uploadMulitple = (file,setProgress,setPending,setInfoFields,identifier,fileArray) => {
  let filesArray=[]
  console.log(file)
  Object.entries(file).forEach(element => {
    // element
    const fileName=element?.[1]?.name.split('.')[0]
    const fileExtension=element?.[1]?.name.split('.')[1]
    const path=`${settings.navbar.center.name}/${identifier}-${fileName}.${fileExtension}`
    filesArray.push({file:element?.[1],name:fileName,path:path})
  });

  // console.log(filesArray)
  
  filesArray.forEach(element => {

  console.log(element)

  setPending(false)
  
  const storageRef = ref(storage, element?.path);
  
  const uploadTask = uploadBytesResumable(storageRef, element?.file);

  try {
      uploadTask.on('state_changed', 
        (snapshot) => {
          const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
          setProgress(progress)
          // console.log('Upload is ' + progress + '% done');
          switch (snapshot.state) {
            case 'paused':
              setPending(false)
              // console.log('Upload is paused');
              break;
            case 'running':
              // console.log('Upload is running');
              setPending(true)
              break;
          }
        }, 
        (error) => {
          return error
          // Handle unsuccessful uploads
        }, 
        () => {
          // Handle successful uploads on complete
          // For instance, get the download URL: https://firebasestorage.googleapis.com/...
          getDownloadURL(uploadTask.snapshot.ref).then((downloadURL) => {
            console.log('File available at', downloadURL);
          // setInfoFields(prev=>[...prev,{image:downloadURL,path:element?.path}])
          setInfoFields(prev=>[...prev,{id:prev.length+1,name:element?.name,url:downloadURL,path:element?.path}])
            // fileUrl=downloadURL
          });
          setPending(false)
        }
      );
      // setFileUrl(fileUrl)
  } catch (error) {
      console.log(error)
  }
  
  });
}

const deleteFiles = (path) => {
  const desertRef = ref(storage, path);

  // Delete the file
  deleteObject(desertRef).then(() => {
    // File deleted successfully
  }).catch((error) => {
    // Uh-oh, an error occurred!
  });
}

export {upload,uploadMulitple,deleteFiles}