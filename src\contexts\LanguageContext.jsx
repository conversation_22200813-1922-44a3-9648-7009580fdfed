'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import { t, formatCurrency, formatDate } from '@/libs/i18n/translations';

const LanguageContext = createContext();

export function LanguageProvider({ children }) {
  const [locale, setLocale] = useState('en');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load saved language preference from localStorage
    const savedLocale = localStorage.getItem('preferred-language');
    if (savedLocale && ['en', 'tsn'].includes(savedLocale)) {
      setLocale(savedLocale);
    } else {
      // Detect browser language
      const browserLang = navigator.language.toLowerCase();
      if (browserLang.startsWith('tn') || browserLang.includes('setswana')) {
        setLocale('tsn');
      }
    }
    setLoading(false);
  }, []);

  const changeLanguage = (newLocale) => {
    if (['en', 'tsn'].includes(newLocale)) {
      setLocale(newLocale);
      localStorage.setItem('preferred-language', newLocale);
      
      // Update document language attribute
      document.documentElement.lang = newLocale === 'tsn' ? 'tn' : 'en';
    }
  };

  const translate = (key, params = {}) => {
    return t(key, locale, params);
  };

  const formatPrice = (amount) => {
    return formatCurrency(amount, locale);
  };

  const formatDateLocale = (date, format = 'short') => {
    return formatDate(date, locale, format);
  };

  const value = {
    locale,
    changeLanguage,
    translate,
    formatPrice,
    formatDate: formatDateLocale,
    isRTL: false, // Neither English nor Setswana are RTL
    loading
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <LanguageContext.Provider value={value}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}

// HOC for components that need translation
export function withTranslation(Component) {
  return function TranslatedComponent(props) {
    const { translate, formatPrice, formatDate, locale } = useLanguage();
    
    return (
      <Component
        {...props}
        t={translate}
        formatPrice={formatPrice}
        formatDate={formatDate}
        locale={locale}
      />
    );
  };
}

// Hook for easy translation access
export function useTranslation() {
  const { translate, formatPrice, formatDate, locale } = useLanguage();
  
  return {
    t: translate,
    formatPrice,
    formatDate,
    locale
  };
}
