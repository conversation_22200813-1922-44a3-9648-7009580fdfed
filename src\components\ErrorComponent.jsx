import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React from 'react'

export default function ErrorComponent({reset}) {
  const router=useRouter()
  return (
    <div className='flex flex-col gap-3 max-w-[240px] w-fit px-5 h-fit items-center p-4 rounded-lg bg-gray-100 shadow-lg'>
      <Link href={`/`} className='flex items-center tracking-[4px] text-2xl'>luyari</Link>
      <button
        className='flex underline text-sm font-light'
          onClick={
          // Attempt to recover by trying to re-render the segment
          () => reset()
          }
      >
          Try again
      </button>
      <button
        className='flex underline text-sm font-light'
          onClick={
            // Attempt to recover by trying to re-render the segment
            () => router.refresh()
          }
      >
        click here to refresh page
      </button>
    </div>
  )
}
