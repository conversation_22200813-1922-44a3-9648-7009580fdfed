import mongoose from 'mongoose';
const { Schema } = mongoose;

const wishlistItemSchema = new Schema({
    productId: { type: Schema.Types.ObjectId, ref: 'Product', required: true },
    addedAt: { type: Date, default: Date.now },
});

const wishlistSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    items: [wishlistItemSchema],
}, { timestamps: true });

// Ensure one wishlist per user
wishlistSchema.index({ userId: 1 }, { unique: true });

export const Wishlist = mongoose.models.Wishlist || mongoose.model('Wishlist', wishlistSchema);
