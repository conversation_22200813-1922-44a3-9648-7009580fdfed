import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

export default async function ProductsComponent({data}) {
  // console.log('ProductsComponent:-',data)
  return (
    <div className='productsList flex flex-wrap items-center gap-10 w-full h-fit overflow-hidden'>
      <div className='flex uppercase w-full mt-5 text-3xl items-center justify-center'><span>fresh arrivals</span></div>
      <div className='flex relative w-full h-fit items-center flex-wrap gap-2'>
        {data?.map((item,index)=>
          // <div key={index} className='flex flex-col md:w-1/5 w-1/2 gap-2 md:h-[460px] h-[320px]'>
          <div key={index} className='flex flex-col md:w-[calc(20%-8px)] w-[calc(50%-8px)] gap-2 md:h-[460px] h-[320px]'>
            <Link href={`/amieshop/product/${item?._id}`} className='flex relative w-full h-4/5 bg-gray-100 items-center justify-center rounded-md'>
              <Image className='object-cover aspect-auto' src={item?.images ? item?.images[0]?.image : null} alt='product image' fill/>
            </Link>
            <div className='flex flex-col items-start gap-2 justify-center w-full h-1/5'>
              <span className='flex text-xl'>{item?.title}</span>
              <span>{item?.price}</span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}