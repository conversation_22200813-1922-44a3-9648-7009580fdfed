import Link from 'next/link'
import React from 'react'

export default function DashboardUserSummary({data}) {
  // console.log('DashboardProductSummary:-',data)
  return (
    <Link href={`/koreanshop/dashboard/users/${data?._id}`} className='flex w-full text-sm gap-2 rounded h-16 border-[1px] border-gray-300 p-1'>
      <span className='flex flex-[1] items-center justify-center'>{data?.image}</span>
      <span className='hidden md:flex flex-[1] items-center justify-start truncate'>{data?._id}</span>
      <span className='flex flex-[1] items-center justify-center'>{data?.username}</span>
      <span className='hidden md:flex flex-[1] items-center justify-center'>{data?.phone}</span>
      <span className='flex flex-[1] items-center justify-center text-center'>{new Date(data?.createdAt).toDateString().slice(4)}</span>
    </Link>
  )
}
