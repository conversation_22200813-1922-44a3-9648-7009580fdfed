import connectToDbAmieShop from "@/libs/mongoDb/connectToDbAmieShop";
import { User } from "@/libs/mongoDb/Models/User";
import { NextResponse } from "next/server";
import bcrypt from "bcrypt";

// GET ALL USERS
export async function POST(req,params) {
    const body=await req.json()
    // console.log(body)
    await connectToDbAmieShop()
    try {
        // CHECK IF CREDENTIALS ARE VALID
        if(!body.password || !body.email) return NextResponse.json('please provide crendetials',{status:401})
            
        // CHECK IF USER EXITS IN DB
        const userExits=await User.findOne({$or:[{email:body.email},{username:body.username}]})
        // console.log('user found',userExits)
    
        // HASH USER PASSWORD
        // console.log('password matched',body.password, userExits.password)
        const passwordsMatch = bcrypt.compareSync(body.password.toString(), userExits.password)
        if(!passwordsMatch)return NextResponse.json('please provide crendetials',{status:401})
        
        // RETURN USER
        const {password, ...others}=userExits._doc
        // console.log('login api route data found',others)
        return NextResponse.json(others, { status: 201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get users',{status:501})
    }
}