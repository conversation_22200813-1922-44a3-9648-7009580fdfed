import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import AccountLayout from '@/components/AccountLayout';
import WishlistPage from '@/components/WishlistPage';

export const metadata = {
  title: 'My Wishlist - SkincareAlert',
  description: 'Manage your saved products',
};

export default async function Wishlist() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/account/wishlist');
  }

  return (
    <AccountLayout>
      <WishlistPage userId={session.user.id} />
    </AccountLayout>
  );
}
