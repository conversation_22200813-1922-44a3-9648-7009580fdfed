'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { 
  ArrowLeftIcon,
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  MapPinIcon,
  DownloadIcon,
  RefreshCwIcon
} from 'lucide-react';

export default function OrderTrackingPage({ order: initialOrder }) {
  const [order, setOrder] = useState(initialOrder);
  const [loading, setLoading] = useState(false);

  const refreshOrder = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/orders/${order._id}`);
      if (response.ok) {
        const data = await response.json();
        setOrder(data.order);
      }
    } catch (error) {
      console.error('Error refreshing order:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'delivered':
        return <CheckCircleIcon className="w-6 h-6 text-green-500" />;
      case 'shipped':
        return <TruckIcon className="w-6 h-6 text-blue-500" />;
      default:
        return <ClockIcon className="w-6 h-6 text-yellow-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'text-green-600 bg-green-100';
      case 'shipped':
        return 'text-blue-600 bg-blue-100';
      case 'processing':
        return 'text-yellow-600 bg-yellow-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const orderTimeline = [
    {
      status: 'pending',
      label: 'Order Placed',
      date: order.createdAt,
      completed: true,
      description: 'Your order has been received and is being processed.'
    },
    {
      status: 'confirmed',
      label: 'Order Confirmed',
      date: order.confirmedAt,
      completed: ['confirmed', 'processing', 'shipped', 'delivered'].includes(order.status),
      description: 'Your order has been confirmed and payment processed.'
    },
    {
      status: 'processing',
      label: 'Processing',
      date: order.status === 'processing' ? new Date() : null,
      completed: ['processing', 'shipped', 'delivered'].includes(order.status),
      description: 'Your order is being prepared for shipment.'
    },
    {
      status: 'shipped',
      label: 'Shipped',
      date: order.shippedAt,
      completed: ['shipped', 'delivered'].includes(order.status),
      description: order.trackingNumber 
        ? `Your order has been shipped. Tracking: ${order.trackingNumber}`
        : 'Your order has been shipped.'
    },
    {
      status: 'delivered',
      label: 'Delivered',
      date: order.deliveredAt,
      completed: order.status === 'delivered',
      description: 'Your order has been delivered successfully.'
    }
  ];

  const handleDownloadInvoice = async () => {
    try {
      const response = await fetch(`/api/orders/${order._id}/invoice`, {
        method: 'GET',
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `invoice-${order.orderNumber}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error downloading invoice:', error);
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <Link
            href="/account/orders"
            className="text-gray-400 hover:text-gray-600"
          >
            <ArrowLeftIcon className="w-6 h-6" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Order #{order.orderNumber}
            </h1>
            <p className="text-gray-600">
              Placed on {new Date(order.createdAt).toLocaleDateString()}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={refreshOrder}
            disabled={loading}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-900 disabled:opacity-50"
          >
            <RefreshCwIcon className={`w-5 h-5 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>

          {order.status === 'delivered' && (
            <button
              onClick={handleDownloadInvoice}
              className="flex items-center space-x-2 bg-gray-900 text-white px-4 py-2 rounded-lg hover:bg-gray-800"
            >
              <DownloadIcon className="w-5 h-5" />
              <span>Download Invoice</span>
            </button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Order Timeline */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Order Status</h2>
            
            <div className="space-y-6">
              {orderTimeline.map((step, index) => (
                <div key={step.status} className="flex items-start space-x-4">
                  <div className={`flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center ${
                    step.completed ? 'bg-green-100' : 'bg-gray-100'
                  }`}>
                    {step.completed ? (
                      <CheckCircleIcon className="w-6 h-6 text-green-600" />
                    ) : (
                      <div className="w-3 h-3 bg-gray-400 rounded-full" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h3 className={`font-medium ${
                        step.completed ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {step.label}
                      </h3>
                      {step.date && (
                        <span className="text-sm text-gray-500">
                          {new Date(step.date).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                    <p className={`text-sm mt-1 ${
                      step.completed ? 'text-gray-600' : 'text-gray-400'
                    }`}>
                      {step.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Order Items */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-6">Order Items</h2>
            
            <div className="space-y-4">
              {order.items.map((item) => (
                <div key={item._id} className="flex items-center space-x-4 p-4 border border-gray-100 rounded-lg">
                  <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden">
                    {item.image || item.productId?.thumbnail ? (
                      <Image
                        src={item.image || item.productId.thumbnail}
                        alt={item.title}
                        width={64}
                        height={64}
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-xs">
                        No image
                      </div>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">
                      {item.title}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {item.brand} • Qty: {item.quantity}
                    </p>
                    {item.productId?.slug && (
                      <Link
                        href={`/products/${item.productId.slug}`}
                        className="text-sm text-blue-600 hover:text-blue-800"
                      >
                        View Product →
                      </Link>
                    )}
                  </div>
                  
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      P{(item.price * item.quantity).toFixed(2)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Order Summary & Shipping */}
        <div className="space-y-6">
          {/* Order Summary */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
            
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Subtotal</span>
                <span className="text-gray-900">P{order.subtotal.toFixed(2)}</span>
              </div>
              
              {order.shippingCost > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Shipping</span>
                  <span className="text-gray-900">P{order.shippingCost.toFixed(2)}</span>
                </div>
              )}
              
              {order.taxAmount > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax</span>
                  <span className="text-gray-900">P{order.taxAmount.toFixed(2)}</span>
                </div>
              )}
              
              {order.discountAmount > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discount</span>
                  <span>-P{order.discountAmount.toFixed(2)}</span>
                </div>
              )}
              
              <div className="border-t pt-3">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total</span>
                  <span>P{order.total.toFixed(2)}</span>
                </div>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t">
              <span className={`inline-block px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(order.status)}`}>
                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
              </span>
            </div>
          </div>

          {/* Shipping Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Information</h3>
            
            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Delivery Address</h4>
                <div className="text-gray-600 text-sm">
                  <p>{order.shippingAddress.firstName} {order.shippingAddress.lastName}</p>
                  {order.shippingAddress.company && (
                    <p>{order.shippingAddress.company}</p>
                  )}
                  <p>{order.shippingAddress.address1}</p>
                  {order.shippingAddress.address2 && (
                    <p>{order.shippingAddress.address2}</p>
                  )}
                  <p>
                    {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.postalCode}
                  </p>
                  <p>{order.shippingAddress.country}</p>
                  {order.shippingAddress.phone && (
                    <p>{order.shippingAddress.phone}</p>
                  )}
                </div>
              </div>

              {order.shippingMethod && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Shipping Method</h4>
                  <p className="text-gray-600 text-sm">{order.shippingMethod}</p>
                </div>
              )}

              {order.trackingNumber && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Tracking Number</h4>
                  <p className="text-gray-900 font-mono text-sm">{order.trackingNumber}</p>
                  {order.carrier && (
                    <p className="text-gray-600 text-sm">Carrier: {order.carrier}</p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Payment Information */}
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Information</h3>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Method</span>
                <span className="text-gray-900">{order.paymentMethod}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Payment Status</span>
                <span className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${
                  order.paymentStatus === 'paid' 
                    ? 'text-green-600 bg-green-100'
                    : order.paymentStatus === 'failed'
                    ? 'text-red-600 bg-red-100'
                    : 'text-yellow-600 bg-yellow-100'
                }`}>
                  {order.paymentStatus.charAt(0).toUpperCase() + order.paymentStatus.slice(1)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
