import nodemailer from 'nodemailer';

// Create transporter
const createTransporter = () => {
  return nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: process.env.SMTP_PORT || 587,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  });
};

// Email templates
const emailTemplates = {
  orderConfirmation: (order, user) => ({
    subject: `Order Confirmation - #${order.orderNumber}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">SkincareAlert</h1>
          <p style="color: #666; margin: 5px 0;">Premium Skincare Products</p>
        </div>
        
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Thank you for your order!</h2>
          <p>Hi ${user.name || 'Valued Customer'},</p>
          <p>We've received your order and it's being processed. Here are the details:</p>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Order #${order.orderNumber}</h3>
            <p><strong>Order Date:</strong> ${new Date(order.createdAt).toLocaleDateString()}</p>
            <p><strong>Total:</strong> P${order.total.toFixed(2)}</p>
            <p><strong>Payment Status:</strong> ${order.paymentStatus}</p>
          </div>
          
          <h3>Order Items:</h3>
          <div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
            ${order.items.map(item => `
              <div style="padding: 15px; border-bottom: 1px solid #eee;">
                <strong>${item.title}</strong><br>
                <span style="color: #666;">Brand: ${item.brand}</span><br>
                <span style="color: #666;">Quantity: ${item.quantity} × P${item.price.toFixed(2)}</span>
              </div>
            `).join('')}
          </div>
          
          <div style="margin-top: 30px; padding: 20px; background-color: #e3f2fd; border-radius: 8px;">
            <h3 style="margin-top: 0; color: #1976d2;">What's Next?</h3>
            <p>• We'll send you a shipping confirmation with tracking details once your order ships</p>
            <p>• You can track your order status in your account dashboard</p>
            <p>• If you have any questions, reply to this email or contact our support team</p>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.NEXTAUTH_URL}/account/orders/${order._id}" 
               style="background-color: #333; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Track Your Order
            </a>
          </div>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
          <p>Thank you for choosing SkincareAlert!</p>
          <p>Gaborone, Botswana | <EMAIL></p>
        </div>
      </div>
    `
  }),

  shippingUpdate: (order, user) => ({
    subject: `Your order #${order.orderNumber} has shipped!`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">SkincareAlert</h1>
          <p style="color: #666; margin: 5px 0;">Premium Skincare Products</p>
        </div>
        
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Your order is on its way! 📦</h2>
          <p>Hi ${user.name || 'Valued Customer'},</p>
          <p>Great news! Your order has been shipped and is on its way to you.</p>
          
          <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #2e7d32;">Shipping Details</h3>
            <p><strong>Order #:</strong> ${order.orderNumber}</p>
            <p><strong>Tracking Number:</strong> ${order.trackingNumber || 'Will be provided soon'}</p>
            <p><strong>Carrier:</strong> ${order.carrier || 'Standard Shipping'}</p>
            <p><strong>Estimated Delivery:</strong> 3-5 business days</p>
          </div>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
            <h3 style="margin-top: 0;">Delivery Address</h3>
            <p>
              ${order.shippingAddress.firstName} ${order.shippingAddress.lastName}<br>
              ${order.shippingAddress.address1}<br>
              ${order.shippingAddress.address2 ? order.shippingAddress.address2 + '<br>' : ''}
              ${order.shippingAddress.city}, ${order.shippingAddress.state} ${order.shippingAddress.postalCode}<br>
              ${order.shippingAddress.country}
            </p>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.NEXTAUTH_URL}/account/orders/${order._id}" 
               style="background-color: #333; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Track Your Package
            </a>
          </div>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
          <p>Thank you for choosing SkincareAlert!</p>
          <p>Gaborone, Botswana | <EMAIL></p>
        </div>
      </div>
    `
  }),

  abandonedCart: (cart, user) => ({
    subject: 'You left something beautiful behind! 💄',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">SkincareAlert</h1>
          <p style="color: #666; margin: 5px 0;">Premium Skincare Products</p>
        </div>
        
        <div style="padding: 30px 20px;">
          <h2 style="color: #333;">Don't let your glow slip away! ✨</h2>
          <p>Hi ${user.name || 'Beautiful'},</p>
          <p>We noticed you left some amazing skincare products in your cart. Your skin deserves the best care!</p>
          
          <div style="background-color: #fff3e0; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #f57c00;">Your Cart Items:</h3>
            ${cart.items.map(item => `
              <div style="padding: 10px 0; border-bottom: 1px solid #eee;">
                <strong>${item.title}</strong><br>
                <span style="color: #666;">P${item.price.toFixed(2)}</span>
              </div>
            `).join('')}
            <div style="margin-top: 15px; font-size: 18px; font-weight: bold;">
              Total: P${cart.total.toFixed(2)}
            </div>
          </div>
          
          <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #2e7d32;">Special Offer! 🎁</h3>
            <p>Complete your purchase in the next 24 hours and get <strong>10% off</strong> with code: <strong>COMEBACK10</strong></p>
          </div>
          
          <div style="text-align: center; margin-top: 30px;">
            <a href="${process.env.NEXTAUTH_URL}/cart" 
               style="background-color: #333; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              Complete Your Purchase
            </a>
          </div>
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
          <p>Your skin will thank you! 💖</p>
          <p>Gaborone, Botswana | <EMAIL></p>
        </div>
      </div>
    `
  }),

  newsletter: (content, user) => ({
    subject: content.subject,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
          <h1 style="color: #333; margin: 0;">SkincareAlert</h1>
          <p style="color: #666; margin: 5px 0;">Premium Skincare Products</p>
        </div>
        
        <div style="padding: 30px 20px;">
          ${content.html}
        </div>
        
        <div style="background-color: #f8f9fa; padding: 20px; text-align: center; color: #666; font-size: 14px;">
          <p>Thank you for being part of our skincare community!</p>
          <p>Gaborone, Botswana | <EMAIL></p>
          <p><a href="${process.env.NEXTAUTH_URL}/unsubscribe?token=${user.unsubscribeToken}" style="color: #666;">Unsubscribe</a></p>
        </div>
      </div>
    `
  })
};

// Send email function
export const sendEmail = async (to, template, data) => {
  try {
    const transporter = createTransporter();
    const emailContent = emailTemplates[template](data.order || data.cart || data.content, data.user);

    const mailOptions = {
      from: `"SkincareAlert" <${process.env.SMTP_USER}>`,
      to,
      subject: emailContent.subject,
      html: emailContent.html,
    };

    const result = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', result.messageId);
    return { success: true, messageId: result.messageId };

  } catch (error) {
    console.error('Email sending error:', error);
    return { success: false, error: error.message };
  }
};

// Bulk email function for newsletters
export const sendBulkEmail = async (recipients, template, data) => {
  const results = [];
  
  for (const recipient of recipients) {
    try {
      const result = await sendEmail(recipient.email, template, {
        ...data,
        user: recipient
      });
      results.push({ email: recipient.email, ...result });
      
      // Add delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      results.push({ email: recipient.email, success: false, error: error.message });
    }
  }
  
  return results;
};
