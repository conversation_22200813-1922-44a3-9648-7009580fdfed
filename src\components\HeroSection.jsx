'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { ChevronLeftIcon, ChevronRightIcon } from 'lucide-react';

const heroSlides = [
  {
    id: 1,
    title: "Twenty Twenty-Fine is the year of skincare!",
    subtitle: "2025 has been dubbed Twenty Twenty-Fine and for Skincare Alert that starts with a good skincare and bodycare routine. Unlock another level of self confidence by shopping your must have skincare products here to look and feel your best!",
    buttonText: "Shop",
    buttonLink: "/products",
    image: "/images/hero-1.jpg",
    bgColor: "bg-gradient-to-r from-pink-50 to-purple-50"
  },
  {
    id: 2,
    title: "Affordable and effective skincare for the everyday person",
    subtitle: "Skincare Alert is an online retailer of various trusted skincare brands of effective and affordable products. We love skin-education, which is why we believe in educating our customers about their skin, how it works and the products.",
    buttonText: "Learn More",
    buttonLink: "/about",
    image: "/images/hero-2.jpg",
    bgColor: "bg-gradient-to-r from-blue-50 to-green-50"
  }
];

export default function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
  };

  return (
    <div className="relative h-[70vh] md:h-[80vh] overflow-hidden">
      {/* Announcement Bar */}
      <div className="bg-gray-900 text-white text-center py-2 text-sm">
        Free delivery for order of P700 and above. Nationwide delivery.
      </div>

      {/* Hero Slider */}
      <div className="relative h-full">
        {heroSlides.map((slide, index) => (
          <div
            key={slide.id}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentSlide ? 'opacity-100' : 'opacity-0'
            } ${slide.bgColor}`}
          >
            <div className="container mx-auto px-4 h-full flex items-center">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center w-full">
                {/* Text Content */}
                <div className="space-y-6 text-center lg:text-left">
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                    {slide.title}
                  </h1>
                  <p className="text-lg md:text-xl text-gray-600 max-w-2xl">
                    {slide.subtitle}
                  </p>
                  <div className="pt-4">
                    <Link
                      href={slide.buttonLink}
                      className="inline-block bg-gray-900 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:bg-gray-800 transition-colors duration-300 transform hover:scale-105"
                    >
                      {slide.buttonText}
                    </Link>
                  </div>
                </div>

                {/* Image */}
                <div className="relative h-64 md:h-96 lg:h-full">
                  <div className="w-full h-full bg-gray-200 rounded-lg flex items-center justify-center">
                    <span className="text-gray-500">Hero Image {slide.id}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Navigation Arrows */}
        <button
          onClick={prevSlide}
          className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-all duration-300"
        >
          <ChevronLeftIcon className="w-6 h-6 text-gray-900" />
        </button>
        <button
          onClick={nextSlide}
          className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white p-2 rounded-full shadow-lg transition-all duration-300"
        >
          <ChevronRightIcon className="w-6 h-6 text-gray-900" />
        </button>

        {/* Slide Indicators */}
        <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {heroSlides.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                index === currentSlide ? 'bg-gray-900' : 'bg-gray-400'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
