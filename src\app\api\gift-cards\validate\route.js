import { NextResponse } from 'next/server';
import { GiftCard } from '@/libs/mongoDb/Models/GiftCard';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const { code, orderAmount } = await request.json();

    if (!code) {
      return NextResponse.json(
        { error: 'Gift card code is required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Find the gift card
    const giftCard = await GiftCard.findValidGiftCard(code);

    if (!giftCard) {
      return NextResponse.json(
        { error: 'Invalid or expired gift card code' },
        { status: 404 }
      );
    }

    // Validate for use
    const validation = giftCard.validateForUse(orderAmount || 0);

    if (!validation.valid) {
      return NextResponse.json(
        { error: validation.errors.join(', ') },
        { status: 400 }
      );
    }

    // Calculate discount amount
    const discountAmount = Math.min(orderAmount || giftCard.currentBalance, giftCard.currentBalance);

    return NextResponse.json({
      valid: true,
      giftCard: {
        id: giftCard._id,
        code: giftCard.code,
        currentBalance: giftCard.currentBalance,
        originalValue: giftCard.originalValue,
        discountAmount,
        expiresAt: giftCard.expiresAt,
        neverExpires: giftCard.neverExpires
      }
    });

  } catch (error) {
    console.error('Gift card validation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
