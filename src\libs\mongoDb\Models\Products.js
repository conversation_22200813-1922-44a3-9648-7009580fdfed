import mongoose from 'mongoose';
const { Schema } = mongoose;

const productSchema = new Schema({
    images:{type:Array,required:true},
    title:{type:String,required:true,unique:true},
    brand:{type:String,required:true},
    price:{type:Number,required:true},
    desc:{type:String,required:true},
    directions:{type:String},
    ingredients:{type:String},
    rating:{type:String},
    thumbnail:{type:String},
    warrantyInformation:{type:String},
    shippingInformation:{type:String},
    returnPolicy:{type:String},
    inStock:{type:Boolean,default:false},
    reviews:{type:Object},
    category:{type:Array,required:true},
    size:{type:Array},
    color:{type:Array},
    tags:{type:Array},
    coments:{type:Array},
},{timestamps:true});

export const Product = mongoose.models.Product||mongoose.model('Product', productSchema)