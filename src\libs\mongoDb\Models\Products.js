import mongoose from 'mongoose';
const { Schema } = mongoose;

const reviewSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
    userName: { type: String, required: true },
    rating: { type: Number, required: true, min: 1, max: 5 },
    comment: { type: String, required: true },
    verified: { type: Boolean, default: false },
    helpful: { type: Number, default: 0 },
}, { timestamps: true });

const variantSchema = new Schema({
    size: { type: String },
    color: { type: String },
    price: { type: Number },
    stock: { type: Number, default: 0 },
    sku: { type: String, unique: true },
});

const productSchema = new Schema({
    images: { type: Array, required: true },
    title: { type: String, required: true, unique: true },
    brand: { type: String, required: true },
    price: { type: Number, required: true },
    salePrice: { type: Number },
    desc: { type: String, required: true },
    shortDesc: { type: String },
    directions: { type: String },
    ingredients: { type: String },
    rating: { type: Number, default: 0 },
    ratingCount: { type: Number, default: 0 },
    thumbnail: { type: String },
    warrantyInformation: { type: String },
    shippingInformation: { type: String },
    returnPolicy: { type: String },
    inStock: { type: Boolean, default: false },
    stock: { type: Number, default: 0 },
    reviews: [reviewSchema],
    category: { type: Array, required: true },
    subcategory: { type: Array },
    skinType: { type: Array }, // e.g., ['dry', 'oily', 'sensitive']
    skinConcern: { type: Array }, // e.g., ['acne', 'aging', 'hyperpigmentation']
    variants: [variantSchema],
    tags: { type: Array },
    featured: { type: Boolean, default: false },
    newProduct: { type: Boolean, default: false },
    bestSeller: { type: Boolean, default: false },
    onSale: { type: Boolean, default: false },
    weight: { type: String },
    dimensions: { type: String },
    seoTitle: { type: String },
    seoDescription: { type: String },
    slug: { type: String, unique: true },
    status: { type: String, enum: ['active', 'inactive', 'draft'], default: 'active' },
}, { timestamps: true });

// Create slug from title before saving
productSchema.pre('save', function(next) {
    if (this.isModified('title') && !this.slug) {
        this.slug = this.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    }
    next();
});

export const Product = mongoose.models.Product || mongoose.model('Product', productSchema);