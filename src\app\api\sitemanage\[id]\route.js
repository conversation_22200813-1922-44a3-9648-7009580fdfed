import { connectToDbAmieShop } from "@/libs/mongoDb/connectToDbAmieShop";
import { Site } from "@/libs/mongoDb/Models/Site";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{params}) {
    const {id}=await params
    // console.log(id)
    await connectToDbAmieShop()
    try {
        const site= await Site.findById(id)
        // console.log('server found:-',site)
        return NextResponse.json(site,{status:201})
    } catch (error) {
        return NextResponse.json('failed to find site',{status:501})
    }
}
export async function PUT(req,{params}) {
    const {id}=await params
    const body=await req.json()
    // console.log(body)
    await connectToDbAmieShop()
    try {
        await Site.findByIdAndUpdate(id,body)
        return NextResponse.json('site details updated',{status:201})
    } catch (error) {
        return NextResponse.json('failed to update site details',{status:501})
    }
}

export async function DELETE(request,{params}) {
    const {id}=await params
    // console.log(id)
    await connectToDbAmieShop()
    try {
        await Site.findByIdAndDelete(id)
        return NextResponse.json('site deleted', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete site details", { status: 501 });
    }
}