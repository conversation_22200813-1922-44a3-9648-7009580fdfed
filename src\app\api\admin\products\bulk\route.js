import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Product } from '@/libs/mongoDb/Models/Products';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.isAdmin && session?.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { action, productIds } = await request.json();

    if (!action || !productIds || !Array.isArray(productIds)) {
      return NextResponse.json(
        { error: 'Action and product IDs are required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    let updateData = {};
    let result;

    switch (action) {
      case 'activate':
        updateData = { status: 'active' };
        break;
      case 'deactivate':
        updateData = { status: 'inactive' };
        break;
      case 'delete':
        result = await Product.deleteMany({ _id: { $in: productIds } });
        return NextResponse.json({
          message: `${result.deletedCount} products deleted successfully`
        });
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    result = await Product.updateMany(
      { _id: { $in: productIds } },
      updateData
    );

    return NextResponse.json({
      message: `${result.modifiedCount} products updated successfully`
    });

  } catch (error) {
    console.error('Bulk action error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
