'use client'
import { settings } from '@/libs/siteEcomSettimngs'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import React from 'react'

export default function DashboardSidebar() {
  const pathName=usePathname()
  // console.log('DashboardSidebar:-',pathName.split('/')[pathName.split('/').length-1])
  return (
    <div className='flex flex-col md:px-4 px-2 w-full h-full rounded-lg bg-white shadow-lg'>
      {settings.dashboard.map((section,index)=>
        <div key={index} className='flex flex-col w-full h-fit gap-2 mt-3 items-center md:items-start'>
          <h1 className='hidden md:block text-xs md:text-base uppercase font-bold'>{section?.name}</h1>
          <div className='md:hidden flex w-full border-[1px] bg-gray-200 my-1'/>
            {section?.list.map((link,index)=>
              <Link key={link?.name} href={link?.name=='home' ? '/amieshop/dashboard' : `/amieshop/dashboard/${link?.name}`} className='flex justify-center md:justify-start text-sm md:px-2 px-4 bg-slate-100 h-9 w-full md:w-[calc(100%-16px)] shadow items-center gap-3 capitalize rounded ml-0 md:ml-2'>
                <div className='flex items-center justify-center'>
                  {link?.icon}
                </div>
                <span className='md:flex hidden'>
                  {link?.name}
                </span>
              </Link>
            )}
        </div>
      )}
    </div>
  )
}
