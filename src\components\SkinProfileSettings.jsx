'use client';

import { useState } from 'react';
import { CheckIcon, SparklesIcon } from 'lucide-react';

const skinTypes = [
  { value: 'dry', label: 'Dry', description: 'Feels tight, rough, or flaky' },
  { value: 'oily', label: 'Oily', description: 'Shiny, greasy, prone to breakouts' },
  { value: 'combination', label: 'Combination', description: 'Oily T-zone, dry cheeks' },
  { value: 'sensitive', label: 'Sensitive', description: 'Easily irritated, reactive' },
  { value: 'normal', label: 'Normal', description: 'Balanced, not too oily or dry' },
];

const skinConcerns = [
  'Acne', 'Aging', 'Dark Spots', 'Dryness', 'Dullness',
  'Fine Lines', 'Hyperpigmentation', 'Large Pores', 'Redness', 'Uneven Texture'
];

const ageRanges = [
  { value: '18-25', label: '18-25 years' },
  { value: '26-35', label: '26-35 years' },
  { value: '36-45', label: '36-45 years' },
  { value: '46-55', label: '46-55 years' },
  { value: '56+', label: '56+ years' },
];

export default function SkinProfileSettings({ user }) {
  const [skinProfile, setSkinProfile] = useState({
    skinType: user?.skinProfile?.skinType || '',
    skinConcerns: user?.skinProfile?.skinConcerns || [],
    ageRange: user?.skinProfile?.ageRange || '',
    allergies: user?.skinProfile?.allergies || '',
    currentRoutine: user?.skinProfile?.currentRoutine || '',
    goals: user?.skinProfile?.goals || '',
    preferences: {
      naturalProducts: user?.skinProfile?.preferences?.naturalProducts || false,
      fraganceFree: user?.skinProfile?.preferences?.fraganceFree || false,
      crueltyfree: user?.skinProfile?.preferences?.crueltyfree || false,
      vegan: user?.skinProfile?.preferences?.vegan || false,
    }
  });

  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleSkinTypeChange = (skinType) => {
    setSkinProfile(prev => ({ ...prev, skinType }));
  };

  const handleConcernToggle = (concern) => {
    setSkinProfile(prev => ({
      ...prev,
      skinConcerns: prev.skinConcerns.includes(concern)
        ? prev.skinConcerns.filter(c => c !== concern)
        : [...prev.skinConcerns, concern]
    }));
  };

  const handlePreferenceToggle = (preference) => {
    setSkinProfile(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [preference]: !prev.preferences[preference]
      }
    }));
  };

  const handleTextChange = (field, value) => {
    setSkinProfile(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    setLoading(true);
    setSuccess(false);

    try {
      const response = await fetch('/api/account/update-skin-profile', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ skinProfile }),
      });

      if (response.ok) {
        setSuccess(true);
        setTimeout(() => setSuccess(false), 3000);
      } else {
        alert('Failed to update skin profile');
      }
    } catch (error) {
      console.error('Skin profile update error:', error);
      alert('Failed to update skin profile');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="max-w-3xl">
        <div className="flex items-center space-x-2 mb-6">
          <SparklesIcon className="w-6 h-6 text-purple-600" />
          <h2 className="text-lg font-semibold text-gray-900">Skin Profile</h2>
        </div>
        
        <p className="text-gray-600 mb-8">
          Help us understand your skin better to provide personalized product recommendations.
        </p>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Success Message */}
          {success && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-center space-x-2">
              <CheckIcon className="w-5 h-5 text-green-600" />
              <span className="text-green-800">Skin profile updated successfully!</span>
            </div>
          )}

          {/* Skin Type */}
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-4">What's your skin type?</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {skinTypes.map((type) => (
                <button
                  key={type.value}
                  type="button"
                  onClick={() => handleSkinTypeChange(type.value)}
                  className={`p-4 border rounded-lg text-left transition-colors ${
                    skinProfile.skinType === type.value
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium text-gray-900">{type.label}</div>
                  <div className="text-sm text-gray-600">{type.description}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Skin Concerns */}
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-4">
              What are your main skin concerns? (Select all that apply)
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {skinConcerns.map((concern) => (
                <button
                  key={concern}
                  type="button"
                  onClick={() => handleConcernToggle(concern)}
                  className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                    skinProfile.skinConcerns.includes(concern)
                      ? 'border-purple-500 bg-purple-50 text-purple-700'
                      : 'border-gray-200 text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {concern}
                </button>
              ))}
            </div>
          </div>

          {/* Age Range */}
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-4">Age Range</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {ageRanges.map((range) => (
                <button
                  key={range.value}
                  type="button"
                  onClick={() => setSkinProfile(prev => ({ ...prev, ageRange: range.value }))}
                  className={`p-3 border rounded-lg text-sm font-medium transition-colors ${
                    skinProfile.ageRange === range.value
                      ? 'border-purple-500 bg-purple-50 text-purple-700'
                      : 'border-gray-200 text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {range.label}
                </button>
              ))}
            </div>
          </div>

          {/* Allergies */}
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">
              Known Allergies or Ingredients to Avoid
            </h3>
            <textarea
              value={skinProfile.allergies}
              onChange={(e) => handleTextChange('allergies', e.target.value)}
              placeholder="List any ingredients you're allergic to or prefer to avoid..."
              rows={3}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Current Routine */}
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">
              Current Skincare Routine
            </h3>
            <textarea
              value={skinProfile.currentRoutine}
              onChange={(e) => handleTextChange('currentRoutine', e.target.value)}
              placeholder="Describe your current skincare routine (morning and evening)..."
              rows={4}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Goals */}
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-2">
              Skincare Goals
            </h3>
            <textarea
              value={skinProfile.goals}
              onChange={(e) => handleTextChange('goals', e.target.value)}
              placeholder="What do you hope to achieve with your skincare routine?"
              rows={3}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>

          {/* Preferences */}
          <div>
            <h3 className="text-md font-medium text-gray-900 mb-4">Product Preferences</h3>
            <div className="space-y-3">
              {[
                { key: 'naturalProducts', label: 'Prefer natural/organic products' },
                { key: 'fraganceFree', label: 'Fragrance-free products only' },
                { key: 'crueltyfree', label: 'Cruelty-free products only' },
                { key: 'vegan', label: 'Vegan products only' },
              ].map((pref) => (
                <label key={pref.key} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={skinProfile.preferences[pref.key]}
                    onChange={() => handlePreferenceToggle(pref.key)}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <span className="text-gray-700">{pref.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Submit Button */}
          <div className="pt-4">
            <button
              type="submit"
              disabled={loading}
              className="bg-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-purple-700 disabled:bg-purple-400 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              <SparklesIcon className="w-5 h-5" />
              <span>{loading ? 'Updating...' : 'Update Skin Profile'}</span>
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
