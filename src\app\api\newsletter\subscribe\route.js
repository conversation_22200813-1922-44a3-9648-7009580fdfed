import { NextResponse } from 'next/server';
import { NewsletterSubscriber } from '@/libs/mongoDb/Models/Newsletter';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';
import { sendEmail } from '@/libs/email/emailService';

export async function POST(request) {
  try {
    await connectToDbAmieShop();

    const { email, firstName, lastName, source = 'website' } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if subscriber already exists
    let subscriber = await NewsletterSubscriber.findOne({ email: email.toLowerCase() });

    if (subscriber) {
      if (subscriber.isActive) {
        return NextResponse.json(
          { error: 'Email is already subscribed to our newsletter' },
          { status: 409 }
        );
      } else {
        // Reactivate subscription
        subscriber.isActive = true;
        subscriber.subscribedAt = new Date();
        subscriber.unsubscribedAt = undefined;

        if (firstName) subscriber.firstName = firstName;
        if (lastName) subscriber.lastName = lastName;
        if (source) subscriber.source = source;

        await subscriber.save();
      }
    } else {
      // Create new subscriber
      const crypto = require('crypto');
      const unsubscribeToken = crypto.randomBytes(32).toString('hex');

      subscriber = new NewsletterSubscriber({
        email: email.toLowerCase(),
        firstName,
        lastName,
        preferences: {
          productUpdates: true,
          promotions: true,
          skincareTips: true,
          newArrivals: true,
        },
        source: source || 'website',
        unsubscribeToken,
        tags: ['new-subscriber']
      });

      await subscriber.save();
    }

    // Send welcome email
    try {
      await sendEmail(subscriber.email, 'newsletter', {
        user: subscriber,
        content: {
          subject: 'Welcome to SkincareAlert Newsletter! 💄',
          html: `
            <h2>Welcome to our skincare community!</h2>
            <p>Hi ${subscriber.firstName || 'Beautiful'},</p>
            <p>Thank you for subscribing to the SkincareAlert newsletter! You're now part of our exclusive community of skincare enthusiasts.</p>

            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>What to expect:</h3>
              <ul>
                <li>✨ Latest skincare tips and trends</li>
                <li>🎁 Exclusive promotions and early access to sales</li>
                <li>🆕 New product launches and recommendations</li>
                <li>📚 Expert skincare advice and tutorials</li>
              </ul>
            </div>

            <p>Get ready to glow! Your skin journey starts here.</p>

            <div style="text-align: center; margin-top: 30px;">
              <a href="${process.env.NEXTAUTH_URL}/products"
                 style="background-color: #333; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                Start Shopping
              </a>
            </div>
          `
        }
      });
    } catch (emailError) {
      console.error('Welcome email error:', emailError);
      // Don't fail the subscription if email fails
    }

    return NextResponse.json({
      message: 'Successfully subscribed to newsletter',
      subscriber: {
        email: subscriber.email,
        firstName: subscriber.firstName,
        lastName: subscriber.lastName,
        preferences: subscriber.preferences
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
