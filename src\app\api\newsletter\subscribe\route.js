import { NextResponse } from 'next/server';
import { Newsletter } from '@/libs/mongoDb/Models/Newsletter';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    await connectToDbAmieShop();
    
    const { email, firstName, lastName, source = 'website' } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingSubscriber = await Newsletter.findOne({ email });
    
    if (existingSubscriber) {
      if (existingSubscriber.status === 'unsubscribed') {
        // Resubscribe
        existingSubscriber.status = 'subscribed';
        existingSubscriber.source = source;
        await existingSubscriber.save();
        
        return NextResponse.json({
          message: 'Successfully resubscribed to newsletter',
          subscriber: existingSubscriber
        });
      } else {
        return NextResponse.json(
          { error: 'Email already subscribed' },
          { status: 409 }
        );
      }
    }

    // Create new subscriber
    const newSubscriber = new Newsletter({
      email,
      firstName,
      lastName,
      source,
      unsubscribeToken: Math.random().toString(36).substr(2, 9)
    });

    await newSubscriber.save();

    return NextResponse.json({
      message: 'Successfully subscribed to newsletter',
      subscriber: newSubscriber
    });

  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
