import { NextResponse } from 'next/server';
import { Product } from '@/libs/mongoDb/Models/Products';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function GET(request) {
  try {
    await connectToDbAmieShop();
    
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('query') || '';
    const brand = searchParams.get('brand');
    const category = searchParams.get('category');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const skinType = searchParams.getAll('skinType');
    const skinConcern = searchParams.getAll('skinConcern');
    const rating = searchParams.get('rating');
    const inStock = searchParams.get('inStock') === 'true';
    const onSale = searchParams.get('onSale') === 'true';
    const sortBy = searchParams.get('sortBy') || 'relevance';
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 24;

    // Build search query
    let searchQuery = { status: 'active' };

    // Text search
    if (query) {
      searchQuery.$or = [
        { title: { $regex: query, $options: 'i' } },
        { desc: { $regex: query, $options: 'i' } },
        { shortDesc: { $regex: query, $options: 'i' } },
        { brand: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } },
        { ingredients: { $regex: query, $options: 'i' } },
      ];
    }

    // Brand filter
    if (brand) {
      searchQuery.brand = brand;
    }

    // Category filter
    if (category) {
      searchQuery.category = { $in: [category] };
    }

    // Price range filter
    if (minPrice || maxPrice) {
      searchQuery.price = {};
      if (minPrice) searchQuery.price.$gte = parseFloat(minPrice);
      if (maxPrice) searchQuery.price.$lte = parseFloat(maxPrice);
    }

    // Skin type filter
    if (skinType.length > 0) {
      searchQuery.skinType = { $in: skinType };
    }

    // Skin concern filter
    if (skinConcern.length > 0) {
      searchQuery.skinConcern = { $in: skinConcern };
    }

    // Rating filter
    if (rating) {
      searchQuery.rating = { $gte: parseFloat(rating) };
    }

    // Stock filter
    if (inStock) {
      searchQuery.inStock = true;
    }

    // Sale filter
    if (onSale) {
      searchQuery.onSale = true;
    }

    // Build sort object
    let sort = {};
    switch (sortBy) {
      case 'price_asc':
        sort.price = 1;
        break;
      case 'price_desc':
        sort.price = -1;
        break;
      case 'rating':
        sort.rating = -1;
        sort.ratingCount = -1;
        break;
      case 'newest':
        sort.createdAt = -1;
        break;
      case 'name':
        sort.title = 1;
        break;
      case 'relevance':
      default:
        // For text search, MongoDB will handle relevance scoring
        if (query) {
          sort.score = { $meta: 'textScore' };
        } else {
          sort.featured = -1;
          sort.createdAt = -1;
        }
        break;
    }

    // Add text index for relevance scoring if searching
    if (query && sortBy === 'relevance') {
      searchQuery.$text = { $search: query };
    }

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Execute search
    const products = await Product.find(searchQuery)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .lean();

    // Get total count for pagination
    const total = await Product.countDocuments(searchQuery);

    // Get aggregated filter data for faceted search
    const facets = await Product.aggregate([
      { $match: { status: 'active' } },
      {
        $facet: {
          brands: [
            { $group: { _id: '$brand', count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
          ],
          categories: [
            { $unwind: '$category' },
            { $group: { _id: '$category', count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
          ],
          priceRange: [
            {
              $group: {
                _id: null,
                minPrice: { $min: '$price' },
                maxPrice: { $max: '$price' }
              }
            }
          ],
          skinTypes: [
            { $unwind: '$skinType' },
            { $group: { _id: '$skinType', count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
          ],
          skinConcerns: [
            { $unwind: '$skinConcern' },
            { $group: { _id: '$skinConcern', count: { $sum: 1 } } },
            { $sort: { _id: 1 } }
          ]
        }
      }
    ]);

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1
      },
      facets: facets[0] || {},
      searchQuery: {
        query,
        brand,
        category,
        minPrice,
        maxPrice,
        skinType,
        skinConcern,
        rating,
        inStock,
        onSale,
        sortBy
      }
    });

  } catch (error) {
    console.error('Search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
