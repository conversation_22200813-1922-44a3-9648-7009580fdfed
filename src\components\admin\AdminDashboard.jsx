'use client';

import Link from 'next/link';
import { 
  CubeIcon,
  ShoppingBagIcon,
  UsersIcon,
  ExclamationTriangleIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  EyeIcon
} from 'lucide-react';

export default function AdminDashboard({ stats }) {
  const {
    totalProducts,
    totalOrders,
    totalCustomers,
    lowStockProducts,
    recentOrders,
    salesData
  } = stats;

  // Calculate revenue trends
  const currentMonth = salesData[0];
  const previousMonth = salesData[1];
  const revenueChange = previousMonth 
    ? ((currentMonth?.totalSales - previousMonth?.totalSales) / previousMonth?.totalSales) * 100
    : 0;

  const statCards = [
    {
      title: 'Total Products',
      value: totalProducts,
      icon: CubeIcon,
      color: 'bg-blue-500',
      href: '/admin/products'
    },
    {
      title: 'Total Orders',
      value: totalOrders,
      icon: ShoppingBagIcon,
      color: 'bg-green-500',
      href: '/admin/orders'
    },
    {
      title: 'Total Customers',
      value: totalCustomers,
      icon: UsersIcon,
      color: 'bg-purple-500',
      href: '/admin/customers'
    },
    {
      title: 'Low Stock Items',
      value: lowStockProducts,
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
      href: '/admin/products?filter=low-stock'
    }
  ];

  const getOrderStatusColor = (status) => {
    switch (status) {
      case 'delivered':
        return 'text-green-600 bg-green-100';
      case 'shipped':
        return 'text-blue-600 bg-blue-100';
      case 'processing':
        return 'text-yellow-600 bg-yellow-100';
      case 'cancelled':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to your admin dashboard</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => (
          <Link
            key={stat.title}
            href={stat.href}
            className="bg-white rounded-lg shadow-sm p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center">
              <div className={`p-3 rounded-lg ${stat.color}`}>
                <stat.icon className="w-6 h-6 text-white" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
              </div>
            </div>
          </Link>
        ))}
      </div>

      {/* Revenue Chart and Recent Orders */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Overview */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Revenue Overview</h2>
            <div className="flex items-center space-x-2">
              {revenueChange >= 0 ? (
                <TrendingUpIcon className="w-5 h-5 text-green-500" />
              ) : (
                <TrendingDownIcon className="w-5 h-5 text-red-500" />
              )}
              <span className={`text-sm font-medium ${
                revenueChange >= 0 ? 'text-green-600' : 'text-red-600'
              }`}>
                {revenueChange >= 0 ? '+' : ''}{revenueChange.toFixed(1)}%
              </span>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600">This Month</p>
              <p className="text-2xl font-bold text-gray-900">
                P{currentMonth?.totalSales?.toFixed(2) || '0.00'}
              </p>
              <p className="text-sm text-gray-500">
                {currentMonth?.orderCount || 0} orders
              </p>
            </div>

            {/* Simple bar chart representation */}
            <div className="space-y-2">
              <p className="text-sm font-medium text-gray-700">Last 6 Months</p>
              <div className="flex items-end space-x-1 h-20">
                {salesData.slice(0, 6).reverse().map((month, index) => {
                  const maxSales = Math.max(...salesData.slice(0, 6).map(m => m.totalSales));
                  const height = (month.totalSales / maxSales) * 100;
                  
                  return (
                    <div
                      key={index}
                      className="flex-1 bg-blue-500 rounded-t"
                      style={{ height: `${height}%` }}
                      title={`${month._id.month}/${month._id.year}: P${month.totalSales.toFixed(2)}`}
                    />
                  );
                })}
              </div>
            </div>
          </div>
        </div>

        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold text-gray-900">Recent Orders</h2>
            <Link
              href="/admin/orders"
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              View All →
            </Link>
          </div>

          <div className="space-y-4">
            {recentOrders.length > 0 ? (
              recentOrders.map((order) => (
                <div
                  key={order._id}
                  className="flex items-center justify-between p-3 border border-gray-100 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div>
                      <p className="font-medium text-gray-900">
                        #{order.orderNumber}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.userId?.name || order.email}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium text-gray-900">
                      P{order.total.toFixed(2)}
                    </p>
                    <span
                      className={`inline-block px-2 py-1 text-xs font-medium rounded-full ${getOrderStatusColor(
                        order.status
                      )}`}
                    >
                      {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <ShoppingBagIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">No recent orders</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link
            href="/admin/products/new"
            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
          >
            <div className="text-center">
              <CubeIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-600">Add New Product</p>
            </div>
          </Link>

          <Link
            href="/admin/orders?status=processing"
            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
          >
            <div className="text-center">
              <ShoppingBagIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-600">Process Orders</p>
            </div>
          </Link>

          <Link
            href="/admin/analytics"
            className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors"
          >
            <div className="text-center">
              <EyeIcon className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm font-medium text-gray-600">View Analytics</p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
}
