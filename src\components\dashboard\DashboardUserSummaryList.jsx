import React from 'react'
import DashboardUserSummary from './DashboardUserSummary'
import Link from 'next/link'

export default function DashboardUserSummaryList({data}) {
  // console.log('DashboardUserSummaryList:-',data[0])
  return (
    <div className='flex flex-col gap-2 w-full h-fit rounded-lg bg-white shadow-lg p-2'>
      <span className='font-medium text-3xl'>User list</span>
      <div className='flex flex-col gap-2 w-full h-fit'>
        <div className='flex w-full uppercase text-xs gap-2 mb-2'>
          <div className='flex flex-[1] items-center justify-center'>profile image</div>
          <div className='hidden md:flex flex-[1] items-center justify-center'>id</div>
          <div className='flex flex-[1] items-center justify-center'>username</div>
          <div className='hidden md:flex flex-[1] items-center justify-center'>phone</div>
          <div className='flex flex-[1] items-center justify-center'>date registered</div>
          <div className='flex flex-[.5] h-full items-center justify-center'>
            actions
          </div>
        </div>
        <div className='flex flex-col gap-2'>
          {data.splice(0,5).map((item,index)=>
            <tr key={index} className='flex w-full text-sm gap-2 rounded h-16 border-[1px] border-gray-300'>
              <DashboardUserSummary data={item}/>
            </tr>
          )}
        </div>
      </div>
    </div>
  )
}
