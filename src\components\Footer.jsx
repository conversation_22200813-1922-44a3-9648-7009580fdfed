import { settings } from '@/libs/siteEcomSettimngs'
import Link from 'next/link'
import React from 'react'
import { FaRegCopyright } from 'react-icons/fa'

export default async function Footer() {
  const dataDb = await fetch(`${settings.url}/api/sitemanage`)
  const data = await dataDb.json()
  // console.log('Footer:',data?.pages)
  return (
    <footer className='foot flex flex-col w-full h-fit md:px-16 px-2 mt-10 gap-5 items-center overflow-hidden mb-10'>
      <hr className='flex w-full border-[1px] border-gray-400'/>
      <div className='flex w-full h-fit flex-col'>
        <div className='foot flex w-full h-full items-center flex-col md:flex-row mt-10'>
          <div className='flex max-h-36 items-center md:items-start w-1/2 flex-col justify-between py-5 flex-wrap'>
            {data?.pages?.length>0 && data?.pages?.map((link,index)=><Link key={index} href={`/shop/${link?.value}`} className='capitalize font-medium'>{link?.value}</Link>)}
          </div>
          <div className='flex h-full w-1/2 items-center md:items-start flex-col gap-3 justify-center'>
            <span className='text-lg font-medium'>Join our mailing list</span>
            <span className='text-center md:text-start'>Promotions, new products and sales. Directly to your inbox.</span>
            <div className='flex w-fit items-center justify-center h-12 border-[1px] border-gray-400'>
                <input type="email" className='flex flex-[8] h-full px-4 border-r-gray-400 outline-none' placeholder='email address'/>
                <div className='flex items-center justify-center bg-amber-900 flex-[1] px-4 uppercase text-white font-medium h-full'>subscirbe</div>
            </div>
          </div>
        </div>
        <div className='copyrite flex-col md:flex-row flex w-full flex-none items-center gap-2 h-1/3 mb-5'>
          <div className='flex flex-col w-full items-center md:mt-0 mt-4 md:items-start'>
            <div className='socials flex md:w-1/2 w-fit gap-3 items-center'>
              {settings.socials.map((item,index)=>
                <Link href={'/amieshop'} key={index} className='flex items-center justify-center bg-gray-200 rounded-full p-2 hover:scale-105 duration-300 ease-linear hover:bg-gray-300 w-fit h-fit text-lg cursor-pointer'>{item.icon}</Link>
              )}
            </div>
          </div>
          <div className='flex w-full h-fit gap-1 items-center justify-center md:justify-start'>
            <FaRegCopyright /> 
            {new Date().getFullYear()},
            <span className='flex capitalize'>{settings.navbar.center.name}</span>
          </div>
        </div>
      </div>
    </footer>
  )
}
