import mongoose from 'mongoose';
const { Schema } = mongoose;

const newsletterSchema = new Schema({
    email: { type: String, required: true, unique: true },
    firstName: { type: String },
    lastName: { type: String },
    status: { type: String, enum: ['subscribed', 'unsubscribed'], default: 'subscribed' },
    source: { type: String, default: 'website' }, // website, popup, checkout, etc.
    preferences: {
        newProducts: { type: Boolean, default: true },
        promotions: { type: Boolean, default: true },
        skincareTips: { type: Boolean, default: true },
    },
    lastEmailSent: { type: Date },
    unsubscribeToken: { type: String },
}, { timestamps: true });

export const Newsletter = mongoose.models.Newsletter || mongoose.model('Newsletter', newsletterSchema);
