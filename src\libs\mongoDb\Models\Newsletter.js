import mongoose from 'mongoose';
const { Schema } = mongoose;

// Newsletter Subscriber Schema
const subscriberSchema = new Schema({
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    index: true
  },

  // Subscriber Details
  firstName: { type: String, trim: true },
  lastName: { type: String, trim: true },

  // Subscription Status
  isActive: { type: Boolean, default: true },
  subscribedAt: { type: Date, default: Date.now },
  unsubscribedAt: { type: Date },

  // Preferences
  preferences: {
    productUpdates: { type: Boolean, default: true },
    promotions: { type: Boolean, default: true },
    skincareTips: { type: Boolean, default: true },
    newArrivals: { type: Boolean, default: true },
  },

  // Segmentation
  tags: [String], // e.g., 'vip', 'new-customer', 'high-value'
  skinType: String,
  interests: [String],

  // Tracking
  source: { type: String, default: 'website' }, // website, popup, checkout, etc.
  unsubscribeToken: { type: String, unique: true, sparse: true },

  // Engagement Metrics
  emailsReceived: { type: Number, default: 0 },
  emailsOpened: { type: Number, default: 0 },
  emailsClicked: { type: Number, default: 0 },
  lastEngagement: { type: Date },
}, { timestamps: true });

// Newsletter Campaign Schema
const campaignSchema = new Schema({
  name: { type: String, required: true, trim: true },
  subject: { type: String, required: true, trim: true },

  // Content
  htmlContent: { type: String, required: true },
  textContent: { type: String },
  previewText: { type: String, trim: true },

  // Campaign Type
  type: {
    type: String,
    enum: ['newsletter', 'promotion', 'announcement', 'tips', 'product-launch'],
    default: 'newsletter'
  },

  // Targeting
  targetSegments: [String], // tags to target
  excludeSegments: [String], // tags to exclude
  targetAllSubscribers: { type: Boolean, default: false },

  // Scheduling
  status: {
    type: String,
    enum: ['draft', 'scheduled', 'sending', 'sent', 'cancelled'],
    default: 'draft'
  },
  scheduledAt: { type: Date },
  sentAt: { type: Date },

  // Analytics
  recipientCount: { type: Number, default: 0 },
  deliveredCount: { type: Number, default: 0 },
  openedCount: { type: Number, default: 0 },
  clickedCount: { type: Number, default: 0 },
  unsubscribedCount: { type: Number, default: 0 },

  // Metadata
  createdBy: { type: Schema.Types.ObjectId, ref: 'User' },
  tags: [String],
  notes: { type: String },
}, { timestamps: true });

export const NewsletterSubscriber = mongoose.models.NewsletterSubscriber ||
  mongoose.model('NewsletterSubscriber', subscriberSchema);

export const NewsletterCampaign = mongoose.models.NewsletterCampaign ||
  mongoose.model('NewsletterCampaign', campaignSchema);

// Keep backward compatibility
export const Newsletter = NewsletterSubscriber;
