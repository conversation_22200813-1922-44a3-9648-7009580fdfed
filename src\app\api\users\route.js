import connectToDbAmieShop from "@/libs/mongoDb/connectToDbAmieShop";
import { User } from "@/libs/mongoDb/Models/User";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req) {
    await connectToDbAmieShop()
    try {
        const users= await User.find({},{password:0,__v:0})
        return NextResponse.json(users,{status:201})
    } catch (error) {
        return NextResponse.json('failed to get users',{status:501})
    }
}

export async function DELETE(request) {
    // console.log(request.json())
    await connectToDbAmieShop()
    try {
        const users=await User.deleteMany()
        return NextResponse.json('deleted all users from database', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete all users from database", { status: 501 });
    }
}