import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Address } from '@/libs/mongoDb/Models/Address';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { addressId } = await request.json();

    if (!addressId) {
      return NextResponse.json(
        { error: 'Address ID is required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Find and delete the address (verify ownership)
    const deletedAddress = await Address.findOneAndDelete({ 
      _id: addressId, 
      userId: session.user.id 
    });

    if (!deletedAddress) {
      return NextResponse.json(
        { error: 'Address not found' },
        { status: 404 }
      );
    }

    // If the deleted address was default, set another address as default
    if (deletedAddress.isDefault) {
      const nextAddress = await Address.findOne({
        userId: session.user.id,
        type: deletedAddress.type
      });

      if (nextAddress) {
        nextAddress.isDefault = true;
        await nextAddress.save();
      }
    }

    return NextResponse.json({
      message: 'Address deleted successfully'
    });

  } catch (error) {
    console.error('Address deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
