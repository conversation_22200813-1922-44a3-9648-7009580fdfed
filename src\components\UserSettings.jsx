'use client'
import React, { useState } from 'react'
import { FaRegUser } from 'react-icons/fa'
import Navbar from './Navbar'
import Link from 'next/link'
import BtnWishlistComponent from './BtnWishlistComponent'
import { IoClose } from 'react-icons/io5'
import BtnCartComponent from './BtnCartComponent'
import { useRouter } from 'next/navigation'
import { settings } from '@/libs/siteEcomSettimngs'

export default function UserSettings() {
    const session=true
    const [showUserStats,setShowUserStats]=useState(false)
    const [btnIndex,setbtnIndex]=useState(null)
    const router=useRouter()
    const handleLoginClick=(index)=>{
      setShowUserStats(!showUserStats)
      index==1 ? router.push('/login') : null
      console.log(index)
    }
    const btnType=['login','register']
    // console.log(session)
  return (
    <>
      {showUserStats
        ? <>
            <div onClick={()=>setShowUserStats(!showUserStats)} className='flex z-10 fixed top-0 right-0 w-full h-full'>
            </div>
            <div className='flex z-20 flex-col fixed top-0 right-0 md:w-[420px] bg-gray-100 gap-5 w-full items-center h-full px-5'>
              <div onClick={()=>setShowUserStats(!showUserStats)} className='flex z-20 cursor-pointer absolute right-2 top-5 rounded-md border-[1px] border-gray-300 p-1'>
                <IoClose className='text-2xl'/>
              </div>
              <div className='flex relative w-full h-[200px] flex-col items-center justify-end'>
                <div className='flex w-32 h-32 shadow items-center justify-center rounded-full overflow-hidden bg-white'>
                  image
                </div>
                <div className='flex w-full items-center justify-between'>
                  <span>username</span>
                  <span>email</span>
                </div>
              </div>
              <hr className='flex w-full border-[1px] border-gray-200'/>
              <div className='flex flex-col items-center w-full h-[400px]'>
                {settings.navbar.links.map((link,index)=>
                  <Link onClick={()=>setbtnIndex(link.id)} key={link?.id} href={`/${link?.name}`} className={`capitalize active:underline text-lg ${link?.id==btnIndex && 'underline'}`}>
                    {link?.name}
                  </Link>
                )}
                {session && settings.navbar.rightHidden.map((link,index)=>
                  <Link onClick={()=>setbtnIndex(link.id)} key={link?.id} href={`/${link?.name}`} className={`capitalize active:underline text-lg ${link?.id==btnIndex && 'underline'}`}>
                    {link?.name}
                  </Link>
                )}
              </div>
              <div className='flex items-center w-fit gap-4 mb-20 h-fit justify-center'>
                <BtnWishlistComponent/>
                <BtnCartComponent/>
              </div>
              {btnType.map((item,index)=><button key={index} onClick={()=>handleLoginClick(index)} className='absolute bottom-4 text-white uppercase left-0 right-0 mx-auto bg-gray-900 hover:bg-gray-800 duration-300 ease-linear h-10 px-4 w-fit shadow rounded'>{session ? 'login' : 'login'}</button>)}
            </div>
          </>
        : <div onClick={()=>setShowUserStats(!showUserStats)} className='userSettings flex items-center cursor-pointer justify-center w-fit gap-5 border-[1px] border-gray-400 p-2 rounded-full'>
            <div className='flex w-5 h-5 cursor-pointer'>
                <FaRegUser className='text-xl'/>
            </div>
          </div>
      }
    </>
  )
}
Navbar