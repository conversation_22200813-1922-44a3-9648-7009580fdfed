import Link from 'next/link'
import React from 'react'
import ChartCirculer from './ChartCirculer';
import { IoMdTrendingDown, IoMdTrendingUp } from 'react-icons/io';

export default function DashboardSummaryDay({data}) {
  const dataChart = [
    {
      name: '18-24',
      uv: 31.47,
      pv: 2400,
      fill: '#8884d8',
    },
  ];
  const style = {
    top: '50%',
    right: 0,
    transform: 'translate(0, -50%)',
    lineHeight: '24px',
  };
  return (
    <div className='flex  flex-col items-center w-full lg:flex-[1] md:h-full h-fit rounded-lg bg-white shadow-lg lg:p-5 px-2 py-5'>
      <span className='flex flex-[1] items-center uppercase md:text-base lx:text-xl text-gray-300'>total revenue</span>
      <div className='flex md:flex-[4] h-[20svh] w-full items-center justify-center'>
        <ChartCirculer data={data} style={style}/>
      </div>
      <span className='flex flex-[.5] items-center font-medium text-gray-500 texrt-xl'>Total sales maed today</span>
      <span className='flex flex-[2] items-center ml-2 font-medium md:text-4xl text-3xl'>${300}</span>
      <span className='flex flex-[.5] items-center text-wrap w-full font-medium text-sm text-center text-gray-300'>previous transaction  processing, latest payemnts may not be included</span>
      <div className='flex flex-col md:flex-row items-center flex-[1] w-full h-full lg:items-end justify-between'>
        <div className='flex flex-col items-center justify-between'>
          <span className='font-medium text-gray-500'>target</span>
          {true
            ? <div className='flex text-xs md:text-sm items-end w-fit gap-2 text-green-500'>
                <IoMdTrendingUp className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
            : <div className='flex text-xs md:text-sm items-end w-fit gap-2 text-red-500'>
                <IoMdTrendingDown className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
          }
        </div>
        <div className='flex flex-col items-center'>
          <span className='font-medium text-gray-500'>last week</span>
          {true
            ? <div className='flex text-xs md:text-sm items-end w-fit gap-2 text-green-400'>
                <IoMdTrendingUp className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
            : <div className='flex text-xs md:text-sm items-end w-fit gap-2 text-red-400'>
                <IoMdTrendingDown className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
          }
        </div>
        <div className='flex flex-col items-center'>
          <span className='font-medium text-gray-500'>last month</span>
          {true
            ? <div className='flex text-xs md:text-sm items-end w-fit gap-2 text-green-500'>
                <IoMdTrendingUp className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
            : <div className='flex text-xs md:text-sm items-end w-fit gap-2 text-red-500'>
                <IoMdTrendingDown className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
          }
        </div>
      </div>
    </div>
  )
}
