import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import AccountLayout from '@/components/AccountLayout';
import AddressBookPage from '@/components/AddressBookPage';
import { Address } from '@/libs/mongoDb/Models/Address';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getUserAddresses(userId) {
  await connectToDbAmieShop();
  
  const addresses = await Address.find({ userId })
    .sort({ isDefault: -1, createdAt: -1 })
    .lean();

  return JSON.parse(JSON.stringify(addresses));
}

export const metadata = {
  title: 'Address Book - SkincareAlert',
  description: 'Manage your shipping and billing addresses',
};

export default async function AddressesPage() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/account/addresses');
  }

  const addresses = await getUserAddresses(session.user.id);

  return (
    <AccountLayout>
      <AddressBookPage addresses={addresses} />
    </AccountLayout>
  );
}
