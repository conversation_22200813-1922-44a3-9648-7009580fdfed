// Translation dictionaries for multi-language support

export const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.products': 'Products',
    'nav.brands': 'Brands',
    'nav.categories': 'Categories',
    'nav.account': 'Account',
    'nav.cart': 'Cart',
    'nav.wishlist': 'Wishlist',
    'nav.search': 'Search',
    'nav.signin': 'Sign In',
    'nav.signup': 'Sign Up',
    'nav.signout': 'Sign Out',

    // Common
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.add': 'Add',
    'common.remove': 'Remove',
    'common.update': 'Update',
    'common.submit': 'Submit',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.view': 'View',
    'common.download': 'Download',
    'common.refresh': 'Refresh',

    // Product
    'product.price': 'Price',
    'product.sale_price': 'Sale Price',
    'product.brand': 'Brand',
    'product.category': 'Category',
    'product.rating': 'Rating',
    'product.reviews': 'Reviews',
    'product.in_stock': 'In Stock',
    'product.out_of_stock': 'Out of Stock',
    'product.add_to_cart': 'Add to Cart',
    'product.add_to_wishlist': 'Add to Wishlist',
    'product.quick_view': 'Quick View',
    'product.compare': 'Compare',
    'product.description': 'Description',
    'product.ingredients': 'Ingredients',
    'product.how_to_use': 'How to Use',
    'product.skin_type': 'Skin Type',
    'product.skin_concerns': 'Skin Concerns',

    // Cart
    'cart.title': 'Shopping Cart',
    'cart.empty': 'Your cart is empty',
    'cart.item_count': '{count} item(s)',
    'cart.subtotal': 'Subtotal',
    'cart.shipping': 'Shipping',
    'cart.tax': 'Tax',
    'cart.total': 'Total',
    'cart.checkout': 'Checkout',
    'cart.continue_shopping': 'Continue Shopping',
    'cart.remove_item': 'Remove Item',
    'cart.update_quantity': 'Update Quantity',

    // Account
    'account.profile': 'Profile',
    'account.orders': 'Orders',
    'account.addresses': 'Addresses',
    'account.wishlist': 'Wishlist',
    'account.settings': 'Settings',
    'account.logout': 'Logout',
    'account.order_history': 'Order History',
    'account.address_book': 'Address Book',
    'account.skin_profile': 'Skin Profile',

    // Orders
    'order.number': 'Order Number',
    'order.date': 'Order Date',
    'order.status': 'Status',
    'order.total': 'Total',
    'order.items': 'Items',
    'order.shipping_address': 'Shipping Address',
    'order.billing_address': 'Billing Address',
    'order.payment_method': 'Payment Method',
    'order.tracking': 'Track Order',
    'order.invoice': 'Download Invoice',

    // Status
    'status.pending': 'Pending',
    'status.confirmed': 'Confirmed',
    'status.processing': 'Processing',
    'status.shipped': 'Shipped',
    'status.delivered': 'Delivered',
    'status.cancelled': 'Cancelled',

    // Forms
    'form.first_name': 'First Name',
    'form.last_name': 'Last Name',
    'form.email': 'Email',
    'form.phone': 'Phone',
    'form.password': 'Password',
    'form.confirm_password': 'Confirm Password',
    'form.address': 'Address',
    'form.city': 'City',
    'form.state': 'State',
    'form.postal_code': 'Postal Code',
    'form.country': 'Country',
    'form.required': 'Required',

    // Messages
    'message.item_added_to_cart': 'Item added to cart',
    'message.item_removed_from_cart': 'Item removed from cart',
    'message.item_added_to_wishlist': 'Item added to wishlist',
    'message.profile_updated': 'Profile updated successfully',
    'message.order_placed': 'Order placed successfully',
    'message.invalid_credentials': 'Invalid email or password',
  },

  // Setswana translations
  tsn: {
    // Navigation
    'nav.home': 'Gae',
    'nav.products': 'Dithoto',
    'nav.brands': 'Mafoko',
    'nav.categories': 'Mefuta',
    'nav.account': 'Akhaonto',
    'nav.cart': 'Koloi',
    'nav.wishlist': 'Ditakatso',
    'nav.search': 'Batla',
    'nav.signin': 'Tsena',
    'nav.signup': 'Kwala',
    'nav.signout': 'Tswa',

    // Common
    'common.loading': 'Go a loda...',
    'common.error': 'Phoso',
    'common.success': 'Katlego',
    'common.save': 'Boloka',
    'common.cancel': 'Khansela',
    'common.delete': 'Phimola',
    'common.edit': 'Baakanya',
    'common.add': 'Tsenya',
    'common.remove': 'Tlosa',
    'common.update': 'Ntšhafatsa',
    'common.submit': 'Romela',
    'common.close': 'Tswala',
    'common.back': 'Morago',
    'common.next': 'Latelang',
    'common.previous': 'Pele',
    'common.view': 'Bona',
    'common.download': 'Kopolola',
    'common.refresh': 'Ntšhafatsa',

    // Product
    'product.price': 'Tlhwatlhwa',
    'product.sale_price': 'Tlhwatlhwa ya Thekiso',
    'product.brand': 'Leina la Khampani',
    'product.category': 'Mofuta',
    'product.rating': 'Tekolo',
    'product.reviews': 'Ditlhahlobo',
    'product.in_stock': 'Go na le Stock',
    'product.out_of_stock': 'Ga go na Stock',
    'product.add_to_cart': 'Tsenya mo Koloining',
    'product.add_to_wishlist': 'Tsenya mo Ditakatsong',
    'product.quick_view': 'Bona ka Bonako',
    'product.compare': 'Bapisa',
    'product.description': 'Tlhaloso',
    'product.ingredients': 'Metswako',
    'product.how_to_use': 'Kafa o ka Dirisang',
    'product.skin_type': 'Mofuta wa Letlalo',
    'product.skin_concerns': 'Mathata a Letlalo',

    // Cart
    'cart.title': 'Koloi ya go Reka',
    'cart.empty': 'Koloi ya gago e se na sepe',
    'cart.item_count': 'Dilo {count}',
    'cart.subtotal': 'Kakaretso',
    'cart.shipping': 'Romelo',
    'cart.tax': 'Lekgetho',
    'cart.total': 'Kakaretso Yotlhe',
    'cart.checkout': 'Duela',
    'cart.continue_shopping': 'Tswelela o Reka',
    'cart.remove_item': 'Tlosa Selo',
    'cart.update_quantity': 'Fetola Palo',

    // Account
    'account.profile': 'Profaele',
    'account.orders': 'Diodara',
    'account.addresses': 'Diaterese',
    'account.wishlist': 'Ditakatso',
    'account.settings': 'Dithulaganyo',
    'account.logout': 'Tswa',
    'account.order_history': 'Hisitori ya Diodara',
    'account.address_book': 'Buka ya Diaterese',
    'account.skin_profile': 'Profaele ya Letlalo',

    // Orders
    'order.number': 'Nomoro ya Odara',
    'order.date': 'Letlha la Odara',
    'order.status': 'Maemo',
    'order.total': 'Kakaretso',
    'order.items': 'Dilo',
    'order.shipping_address': 'Aterese ya Romelo',
    'order.billing_address': 'Aterese ya Tefo',
    'order.payment_method': 'Mokgwa wa Tefo',
    'order.tracking': 'Latela Odara',
    'order.invoice': 'Kopolola Invoice',

    // Status
    'status.pending': 'E emetse',
    'status.confirmed': 'E netefaditswe',
    'status.processing': 'E baakanyediwa',
    'status.shipped': 'E romelwe',
    'status.delivered': 'E fihlile',
    'status.cancelled': 'E khanseletswe',

    // Forms
    'form.first_name': 'Leina la Ntlha',
    'form.last_name': 'Leina la Bofelo',
    'form.email': 'Imeile',
    'form.phone': 'Mogala',
    'form.password': 'Password',
    'form.confirm_password': 'Netefatsa Password',
    'form.address': 'Aterese',
    'form.city': 'Toropo',
    'form.state': 'Porofense',
    'form.postal_code': 'Khoutu ya Poso',
    'form.country': 'Naga',
    'form.required': 'E a tlhokega',

    // Messages
    'message.item_added_to_cart': 'Selo se tsentswe mo koloining',
    'message.item_removed_from_cart': 'Selo se tlositswe mo koloining',
    'message.item_added_to_wishlist': 'Selo se tsentswe mo ditakatsong',
    'message.profile_updated': 'Profaele e ntšhafaditswe sentle',
    'message.order_placed': 'Odara e beilwe sentle',
    'message.invalid_credentials': 'Imeile kgotsa password e fosagetseng',
  }
};

// Currency formatting for different locales
export const currencies = {
  en: {
    code: 'BWP',
    symbol: 'P',
    format: (amount) => `P${amount.toFixed(2)}`
  },
  tsn: {
    code: 'BWP',
    symbol: 'P',
    format: (amount) => `P${amount.toFixed(2)}`
  }
};

// Date formatting for different locales
export const dateFormats = {
  en: {
    short: 'MM/dd/yyyy',
    long: 'MMMM dd, yyyy',
    time: 'hh:mm a'
  },
  tsn: {
    short: 'dd/MM/yyyy',
    long: 'dd MMMM yyyy',
    time: 'HH:mm'
  }
};

// Helper function to get translation
export const t = (key, locale = 'en', params = {}) => {
  const translation = translations[locale]?.[key] || translations.en[key] || key;
  
  // Replace parameters in translation
  return Object.keys(params).reduce((str, param) => {
    return str.replace(`{${param}}`, params[param]);
  }, translation);
};

// Helper function to format currency
export const formatCurrency = (amount, locale = 'en') => {
  return currencies[locale]?.format(amount) || currencies.en.format(amount);
};

// Helper function to format date
export const formatDate = (date, locale = 'en', format = 'short') => {
  const dateObj = new Date(date);
  
  if (locale === 'tsn') {
    // Setswana date formatting
    const day = dateObj.getDate().toString().padStart(2, '0');
    const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
    const year = dateObj.getFullYear();
    
    if (format === 'short') {
      return `${day}/${month}/${year}`;
    } else {
      const months = [
        'Ferikgong', 'Tlhakole', 'Mopitlwe', 'Moranang',
        'Motsheganong', 'Seetebosigo', 'Phukwi', 'Phatwe',
        'Lwetse', 'Diphalane', 'Ngwanatsele', 'Sedimonthole'
      ];
      return `${day} ${months[dateObj.getMonth()]} ${year}`;
    }
  } else {
    // English date formatting
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: format === 'long' ? 'long' : 'numeric',
      day: 'numeric'
    });
  }
};
