import { connectToDb } from "@/lib/mongoDb/connectToDb";
import { Cart } from "@/lib/mongoDb/Models/Cart";
import { Order } from "@/lib/mongoDb/Models/Order";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{searchParams}) {
    // const filters = await searchParams
    // console.log(filters)
    connectToDb()
    try {
        const cartEntries= await Cart.find()
        return NextResponse.json(cartEntries,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get cart entries',{status:501})
    }
}

export async function POST(req) {
    const body=await req.json()
    // console.log(body)
    connectToDb()
    try {
        const newCartEntry=await Cart(body)
        newCartEntry.save()
        return NextResponse.json(newCartEntry,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to add cart entriy',{status:501})
    }
}

export async function DELETE(request) {
    // console.log(request.json())
    await connectToDb()
    try {
        await Cart.deleteMany()
        return NextResponse.json('deleted cart entries from database', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete all cart entries from database", { status: 501 });
    }
}