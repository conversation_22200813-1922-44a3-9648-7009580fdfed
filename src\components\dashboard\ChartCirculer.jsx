'use client'
import { <PERSON>, <PERSON><PERSON>B<PERSON>, <PERSON><PERSON><PERSON>ar<PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON> } from 'recharts'

export default function ChartCirculer() {
  const data = [
    {
      name: '50+',
      uv: 2.63,
      pv: 5800,
      fill: '#d0ed57',
    },
    {
      name: 'unknown',
      uv: 6.67,
      pv: 4800,
      fill: '#ffc658',
    },
  ];
  const style = {
    top: '50%',
    right: '38%',
    transform: 'translate(0, -50%)',
    lineHeight: '24px',
  };
  return (
    <ResponsiveContainer width="100%" height="100%">
      <RadialBarChart cx="50%" cy="50%" innerRadius="80%" outerRadius="100%" barSize={10} data={data}>
        <RadialBar
          minAngle={15}
          background
          clockWise
          dataKey="uv"
        />
        <Legend iconSize={5} layout="vertical" verticalAlign="middle" wrapperStyle={style} />
      </RadialBarChart>
    </ResponsiveContainer>
  )
}
