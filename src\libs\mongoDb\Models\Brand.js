import mongoose from 'mongoose';
const { Schema } = mongoose;

const brandSchema = new Schema({
    name: { type: String, required: true, unique: true },
    slug: { type: String, unique: true },
    description: { type: String },
    logo: { type: String },
    banner: { type: String },
    website: { type: String },
    featured: { type: Boolean, default: false },
    status: { type: String, enum: ['active', 'inactive'], default: 'active' },
    seoTitle: { type: String },
    seoDescription: { type: String },
    productCount: { type: Number, default: 0 },
}, { timestamps: true });

// Create slug from name before saving
brandSchema.pre('save', function(next) {
    if (this.isModified('name') && !this.slug) {
        this.slug = this.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');
    }
    next();
});

export const Brand = mongoose.models.Brand || mongoose.model('Brand', brandSchema);
