import { settings } from '@/libs/siteSettings';
import Link from 'next/link';
import React from 'react'

export default function NavbarComponent() {
  return (
    <nav className="flex fixed z-50 top-0 left-0 w-full h-20 px-10 items-center justify-between from-black bg-gradient-to-b text-white">
        <Link href={'/'} className="flex items-center text-lg tracking-[6px]">{settings.siteName}</Link>
        <div className='flex items-center h-full w-fit gap-5'>
          {settings.linkBackend?.map((link,index)=>
            <Link href={`/${link?.name}`} key={index} className='flex capitalize text-sm font-medium'>{link?.name}</Link>
          )}
        </div>
    </nav>
  )
}
