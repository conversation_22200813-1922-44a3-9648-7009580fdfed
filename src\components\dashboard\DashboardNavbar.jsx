'use client'
import SearchComponent from '../SearchComponent'
import { usePathname, useRouter } from 'next/navigation'

export default function DashboardNavbar() {
  const pathName=usePathname()
  const router=useRouter()
  // console.log('DashboardNavbar:',pathName.split('/'))
  return (
    <div className='flex w-full h-full rounded-lg bg-white shadow-lg items-center justify-between p-2'>
      <SearchComponent/>
      {pathName.split('/').length-1>2 && <button className='flex px-4 h-10 rounded md:text-sm text-xs bg-gray-900 hover:bg-gray-700 ease-linear duration-300 text-white uppercase items-center justify-center'>
        {pathName.split('/').length-1>3 ? <span onClick={()=>router.back()}>back</span> : <span onClick={()=>router.push(`/amieshop/dashboard/${pathName.split('/')[pathName.split('/').length-1]}/add`)}>add {pathName.split('/')[pathName.split('/').length-1]}</span>}
      </button>}
    </div>
  )
}
