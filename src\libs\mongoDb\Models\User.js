import mongoose from 'mongoose';
const { Schema } = mongoose;

const userSchema = new Schema({
    username:{type:String,required:true,unique:true},
    email:{type:String,required:true,unique:true},
    password:{type:String,required:true},
    isAdmin:{type:<PERSON><PERSON><PERSON>},
    img:{type:Object},
    role:{type:String,default:'user'},
    number:{type:Number},
},{timestamps:true});

export const User = mongoose.models.User||mongoose.model('User', userSchema)