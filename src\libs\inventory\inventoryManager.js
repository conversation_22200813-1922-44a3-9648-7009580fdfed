import { Product } from '@/libs/mongoDb/Models/Products';
import { Order } from '@/libs/mongoDb/Models/Order';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';
import { sendEmail } from '@/libs/email/emailService';

export class InventoryManager {
  
  // Update stock levels
  static async updateStock(productId, quantity, operation = 'set', reason = 'manual_adjustment') {
    try {
      await connectToDbAmieShop();
      
      const product = await Product.findById(productId);
      if (!product) {
        throw new Error('Product not found');
      }

      const oldStock = product.stock;
      let newStock;

      switch (operation) {
        case 'set':
          newStock = quantity;
          break;
        case 'add':
          newStock = oldStock + quantity;
          break;
        case 'subtract':
          newStock = Math.max(0, oldStock - quantity);
          break;
        default:
          throw new Error('Invalid operation');
      }

      // Update product stock
      product.stock = newStock;
      product.inStock = newStock > 0;
      product.lastStockUpdate = new Date();

      // Add to stock history
      if (!product.stockHistory) {
        product.stockHistory = [];
      }

      product.stockHistory.push({
        previousStock: oldStock,
        newStock: newStock,
        change: newStock - oldStock,
        reason: reason,
        timestamp: new Date()
      });

      // Keep only last 50 stock history entries
      if (product.stockHistory.length > 50) {
        product.stockHistory = product.stockHistory.slice(-50);
      }

      await product.save();

      // Check for low stock alerts
      await this.checkLowStockAlert(product);

      // Check for reorder point
      await this.checkReorderPoint(product);

      console.log(`Stock updated for ${product.title}: ${oldStock} → ${newStock}`);
      
      return {
        productId: product._id,
        title: product.title,
        oldStock,
        newStock,
        change: newStock - oldStock
      };

    } catch (error) {
      console.error('Error updating stock:', error);
      throw error;
    }
  }

  // Bulk stock update
  static async bulkUpdateStock(updates) {
    const results = [];
    
    for (const update of updates) {
      try {
        const result = await this.updateStock(
          update.productId,
          update.quantity,
          update.operation || 'set',
          update.reason || 'bulk_update'
        );
        results.push({ success: true, ...result });
      } catch (error) {
        results.push({
          success: false,
          productId: update.productId,
          error: error.message
        });
      }
    }

    return results;
  }

  // Reserve stock for pending orders
  static async reserveStock(productId, quantity, orderId) {
    try {
      await connectToDbAmieShop();
      
      const product = await Product.findById(productId);
      if (!product) {
        throw new Error('Product not found');
      }

      if (product.stock < quantity) {
        throw new Error('Insufficient stock available');
      }

      // Update stock and reserved stock
      product.stock -= quantity;
      product.reservedStock = (product.reservedStock || 0) + quantity;
      product.inStock = product.stock > 0;

      // Add reservation record
      if (!product.reservations) {
        product.reservations = [];
      }

      product.reservations.push({
        orderId,
        quantity,
        reservedAt: new Date(),
        expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
      });

      await product.save();

      console.log(`Reserved ${quantity} units of ${product.title} for order ${orderId}`);
      
      return true;

    } catch (error) {
      console.error('Error reserving stock:', error);
      throw error;
    }
  }

  // Release reserved stock
  static async releaseReservedStock(productId, quantity, orderId) {
    try {
      await connectToDbAmieShop();
      
      const product = await Product.findById(productId);
      if (!product) {
        throw new Error('Product not found');
      }

      // Update stock and reserved stock
      product.stock += quantity;
      product.reservedStock = Math.max(0, (product.reservedStock || 0) - quantity);
      product.inStock = product.stock > 0;

      // Remove reservation record
      if (product.reservations) {
        product.reservations = product.reservations.filter(
          reservation => reservation.orderId.toString() !== orderId.toString()
        );
      }

      await product.save();

      console.log(`Released ${quantity} units of ${product.title} from order ${orderId}`);
      
      return true;

    } catch (error) {
      console.error('Error releasing reserved stock:', error);
      throw error;
    }
  }

  // Clean up expired reservations
  static async cleanupExpiredReservations() {
    try {
      await connectToDbAmieShop();
      
      const products = await Product.find({
        'reservations.0': { $exists: true }
      });

      let cleanedCount = 0;

      for (const product of products) {
        const now = new Date();
        const expiredReservations = product.reservations.filter(
          reservation => reservation.expiresAt < now
        );

        if (expiredReservations.length > 0) {
          // Calculate total expired quantity
          const expiredQuantity = expiredReservations.reduce(
            (total, reservation) => total + reservation.quantity, 0
          );

          // Restore stock
          product.stock += expiredQuantity;
          product.reservedStock = Math.max(0, product.reservedStock - expiredQuantity);
          product.inStock = product.stock > 0;

          // Remove expired reservations
          product.reservations = product.reservations.filter(
            reservation => reservation.expiresAt >= now
          );

          await product.save();
          cleanedCount++;

          console.log(`Cleaned up ${expiredQuantity} expired reservations for ${product.title}`);
        }
      }

      console.log(`Cleaned up expired reservations for ${cleanedCount} products`);
      return cleanedCount;

    } catch (error) {
      console.error('Error cleaning up expired reservations:', error);
      throw error;
    }
  }

  // Check low stock alert
  static async checkLowStockAlert(product) {
    const lowStockThreshold = product.lowStockThreshold || 10;
    
    if (product.stock <= lowStockThreshold && product.stock > 0) {
      // Send low stock alert
      console.log(`Low stock alert: ${product.title} (${product.stock} remaining)`);
      
      // TODO: Send email to admin
      // await this.sendLowStockAlert(product);
    }
  }

  // Check reorder point
  static async checkReorderPoint(product) {
    const reorderPoint = product.reorderPoint || 5;
    
    if (product.stock <= reorderPoint) {
      // Trigger reorder process
      console.log(`Reorder point reached: ${product.title} (${product.stock} remaining)`);
      
      // Create reorder suggestion
      await this.createReorderSuggestion(product);
    }
  }

  // Create reorder suggestion
  static async createReorderSuggestion(product) {
    const reorderQuantity = product.reorderQuantity || 50;
    
    const suggestion = {
      productId: product._id,
      title: product.title,
      currentStock: product.stock,
      suggestedQuantity: reorderQuantity,
      priority: product.stock === 0 ? 'urgent' : 'normal',
      createdAt: new Date()
    };

    // Add to product's reorder suggestions
    if (!product.reorderSuggestions) {
      product.reorderSuggestions = [];
    }

    product.reorderSuggestions.push(suggestion);
    await product.save();

    console.log(`Reorder suggestion created for ${product.title}: ${reorderQuantity} units`);
    
    return suggestion;
  }

  // Get low stock products
  static async getLowStockProducts(threshold = 10) {
    try {
      await connectToDbAmieShop();
      
      const products = await Product.find({
        stock: { $lte: threshold, $gt: 0 },
        status: 'active'
      }).select('title brand stock lowStockThreshold').lean();

      return products;

    } catch (error) {
      console.error('Error getting low stock products:', error);
      throw error;
    }
  }

  // Get out of stock products
  static async getOutOfStockProducts() {
    try {
      await connectToDbAmieShop();
      
      const products = await Product.find({
        stock: 0,
        status: 'active'
      }).select('title brand stock lastStockUpdate').lean();

      return products;

    } catch (error) {
      console.error('Error getting out of stock products:', error);
      throw error;
    }
  }

  // Get inventory report
  static async getInventoryReport() {
    try {
      await connectToDbAmieShop();
      
      const [
        totalProducts,
        inStockProducts,
        outOfStockProducts,
        lowStockProducts,
        totalStockValue
      ] = await Promise.all([
        Product.countDocuments({ status: 'active' }),
        Product.countDocuments({ status: 'active', stock: { $gt: 0 } }),
        Product.countDocuments({ status: 'active', stock: 0 }),
        Product.countDocuments({ status: 'active', stock: { $lte: 10, $gt: 0 } }),
        Product.aggregate([
          { $match: { status: 'active' } },
          { $group: { _id: null, total: { $sum: { $multiply: ['$stock', '$price'] } } } }
        ])
      ]);

      return {
        totalProducts,
        inStockProducts,
        outOfStockProducts,
        lowStockProducts,
        totalStockValue: totalStockValue[0]?.total || 0,
        stockTurnover: await this.calculateStockTurnover(),
        generatedAt: new Date()
      };

    } catch (error) {
      console.error('Error generating inventory report:', error);
      throw error;
    }
  }

  // Calculate stock turnover
  static async calculateStockTurnover() {
    try {
      // Calculate for last 30 days
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const soldProducts = await Order.aggregate([
        {
          $match: {
            status: { $in: ['shipped', 'delivered'] },
            createdAt: { $gte: thirtyDaysAgo }
          }
        },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.productId',
            totalSold: { $sum: '$items.quantity' }
          }
        }
      ]);

      const totalSold = soldProducts.reduce((sum, item) => sum + item.totalSold, 0);
      const averageInventory = await Product.aggregate([
        { $match: { status: 'active' } },
        { $group: { _id: null, avgStock: { $avg: '$stock' } } }
      ]);

      const turnover = averageInventory[0]?.avgStock 
        ? totalSold / averageInventory[0].avgStock 
        : 0;

      return Math.round(turnover * 100) / 100;

    } catch (error) {
      console.error('Error calculating stock turnover:', error);
      return 0;
    }
  }

  // Sync inventory with external systems
  static async syncInventory(externalData) {
    try {
      const results = [];
      
      for (const item of externalData) {
        try {
          const result = await this.updateStock(
            item.productId,
            item.stock,
            'set',
            'external_sync'
          );
          results.push({ success: true, ...result });
        } catch (error) {
          results.push({
            success: false,
            productId: item.productId,
            error: error.message
          });
        }
      }

      console.log(`Synced inventory for ${results.length} products`);
      return results;

    } catch (error) {
      console.error('Error syncing inventory:', error);
      throw error;
    }
  }
}

// Cron job function for inventory maintenance
export async function inventoryMaintenanceCronJob() {
  try {
    console.log('Starting inventory maintenance cron job...');
    
    // Clean up expired reservations
    await InventoryManager.cleanupExpiredReservations();
    
    // Check for low stock alerts
    const lowStockProducts = await InventoryManager.getLowStockProducts();
    if (lowStockProducts.length > 0) {
      console.log(`Found ${lowStockProducts.length} low stock products`);
      // TODO: Send consolidated low stock report to admin
    }
    
    console.log('Inventory maintenance cron job completed.');
    
  } catch (error) {
    console.error('Inventory maintenance cron job failed:', error);
  }
}
