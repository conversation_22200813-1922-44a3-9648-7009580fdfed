'use client'

import { useEffect, useState } from "react"
import BtnTrashComponent from "./BtnTrashComponent"
import { useRouter } from "next/navigation"

function InputText({data,setInfoSubmit}) {
  const [userInfo,setUserInfo]=useState()
  const {state,...others}=data
  // console.log('InputText:',data,userInfo)
  return(
    <input onChange={(e)=>setInfoSubmit(prev=>prev={...prev,[data?.name]:e.target.value})} className="flex outline-none px-2 w-full h-16 border-b-[1px] border-gray-200" {...others}/>
  )
}

function InputAddList({data,dbData,setInfoSubmit,infoSubmit}) {
  const {state,...others}=data
  const [userInfo,setUserInfo]=useState()
  const [list,setList]=useState([])
  const dataList=state
  
  const handleAdd = (e) => {
    e.preventDefault()
    userInfo?.length>0 && setList(prev=>[...prev,{id:prev?.length+1,value:userInfo}])
  }

  const handleRemove = (e,id) => {
    e.preventDefault()
    setList(list?.filter((item,index)=>item?.id!==id))
  }

  useEffect(()=>{setInfoSubmit(prev=>prev={...prev,[data?.name]:list})},[list])

  useEffect(()=>{setList(dbData?.[data?.name])},[dbData])
  
  // console.log('InputAddList:',userInfo)
  return(
    <div className="flex flex-col w-1/2 h-fit items-center justify-center p-4 gap-2">
      <div className="flex w-full items-center h-fit gap-4">
        <input onChange={(e)=>setUserInfo(e.target.value)} className="flex outline-none px-2 w-full h-16 border-b-[1px] " {...others}/>
        <button onClick={handleAdd} className="flex select-none text-sm px-4 h-10 rounded bg-gray-200 capitalize items-center justify-center mt-4">add</button>
      </div>
      <div className="flex w-full min-h-4 flex-wrap max-h-40 overflow-y-auto rounded bg-gray-200 p-2 gap-2">
        {list?.map((item,index)=>
          <div key={item?.id} className="flex select-none p-1 shadow items-center bg-gray-100 justify-center h-fit rounded gap-2">
            <span className="select-none">{item?.value}</span>
            <div className="flex h-fit cursor-pointer shadow rounded bg-gray-50  " onClick={(e)=>handleRemove(e,item?.id)}><BtnTrashComponent style={'text-xs'}/></div>
          </div>
        )}
      </div>
    </div>
  )
}

export default function DashboardManageInputComponent({data}) {
  const [infoSubmit,setInfoSubmit]=useState({
    siteName:'',
    collections:[],
    brands:[],
  })
  const [err,setErr]=useState()
  const [pending,setPending]=useState(false)
  const [showError,setShowError]=useState(false)
  const router=useRouter()

  useEffect(()=>{data?.siteName && setInfoSubmit(data)},[])

  const handleSunmit=async(e)=>{
    e.preventDefault()
    setShowError(false)
    try {
      const res=data?.siteName ? await fetch(`/api/housemodels/sitemanage`,{method:'PUT',body:JSON.stringify(infoSubmit)}) : await fetch(`/api/housemodels/sitemanage`,{method:'POST',body:JSON.stringify(infoSubmit)})
      if(res.ok){
        setShowError(true)
        setErr(await res.json())
      }else{
        setShowError(true)
        setErr('failed to submit details')
      }
      router.refresh()
      router.push('/housemodels/dashboard/manage')
    } catch (error) {
      console.error(error)
    }
  }
  const inputEntries={
    inputText:[
      {state:infoSubmit?.siteName,name:'siteName',placeholder:infoSubmit?.siteName?.length>0 ? infoSubmit?.siteName : 'site name',type:'text'}
    ],
    inputAddToList:[
      {state:infoSubmit?.collections,name:'collections',placeholder:'collections',type:'text'},
      // {state:infoSubmit?.brands,name:'brands',placeholder:'brands',type:'text'},
      {state:infoSubmit?.pages,name:'pages',placeholder:'pages',type:'text'},
    ],
  }
  console.log('DashboardManageInputComponent:',infoSubmit,data)
  return (
    <div className='flex w-full h-full rounded-lg bg-white border-[1px] border-gray-300'>
      <div className="flex flex-col max-w-96 h-full justify-center ml-20 gap-5">
        <div className="flex bg-gray-100 w-full h-2/3">

        </div>
        <div className="flex w-full h-fit">
          <input type="file" className="flex ml-5 w-full items-center justify-center"/>
        </div>
      </div>
      <form onSubmit={handleSunmit} className="flex flex-col gap-5 w-2/3 h-full px-20 items-center justify-center">
        {inputEntries.inputText.map((item,index)=>
          <InputText key={index} setInfoSubmit={setInfoSubmit} data={item}/>
        )}
        <div className="flex w-full h-fit flex-wrap">
          {inputEntries.inputAddToList.map((item,index)=>
            <InputAddList key={index} infoSubmit={infoSubmit} dbData={data} setInfoSubmit={setInfoSubmit} data={item}/>
          )}
        </div>
        <input className="flex w-full text-white cursor-pointer h-12 bg-gray-900 hover:bg-gray-700 duration-300 ease-linear rounded shadow" type="submit" />
        {showError && <span className="flex w-full text-center font-medium">{err}</span>}
      </form>
    </div>
  )
}
