import { NextResponse } from 'next/server';
import { Brand } from '@/libs/mongoDb/Models/Brand';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function GET(request) {
  try {
    await connectToDbAmieShop();
    
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured');
    const status = searchParams.get('status') || 'active';

    // Build query
    let query = { status };

    if (featured === 'true') {
      query.featured = true;
    }

    const brands = await Brand.find(query)
      .sort({ name: 1 })
      .lean();

    return NextResponse.json({ brands });

  } catch (error) {
    console.error('Brands fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request) {
  try {
    await connectToDbAmieShop();
    
    const brandData = await request.json();

    // Validate required fields
    if (!brandData.name) {
      return NextResponse.json(
        { error: 'Brand name is required' },
        { status: 400 }
      );
    }

    const newBrand = new Brand(brandData);
    await newBrand.save();

    return NextResponse.json({
      message: 'Brand created successfully',
      brand: newBrand
    }, { status: 201 });

  } catch (error) {
    console.error('Brand creation error:', error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        { error: 'Brand with this name already exists' },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
