import { notFound } from 'next/navigation';
import ProductImageGallery from '@/components/ProductImageGallery';
import ProductInfo from '@/components/ProductInfo';
import ProductReviews from '@/components/ProductReviews';
import RelatedProducts from '@/components/RelatedProducts';
import Breadcrumb from '@/components/Breadcrumb';
import { Product } from '@/libs/mongoDb/Models/Products';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getProduct(slug) {
  await connectToDbAmieShop();
  
  const product = await Product.findOne({ 
    slug, 
    status: 'active' 
  }).populate('reviews.userId', 'name email').lean();
  
  if (!product) {
    return null;
  }

  return JSON.parse(JSON.stringify(product));
}

async function getRelatedProducts(productId, category, brand) {
  await connectToDbAmieShop();
  
  const relatedProducts = await Product.find({
    _id: { $ne: productId },
    status: 'active',
    $or: [
      { category: { $in: category } },
      { brand: brand }
    ]
  }).limit(8).lean();

  return JSON.parse(JSON.stringify(relatedProducts));
}

export async function generateMetadata({ params }) {
  const product = await getProduct(params.slug);
  
  if (!product) {
    return {
      title: 'Product Not Found',
    };
  }

  return {
    title: `${product.title} - ${product.brand} | SkincareAlert`,
    description: product.shortDesc || product.desc.substring(0, 160),
    keywords: [product.brand, ...product.category, ...product.tags].join(', '),
    openGraph: {
      title: product.title,
      description: product.shortDesc || product.desc.substring(0, 160),
      images: [product.thumbnail || product.images[0]],
      type: 'product',
    },
    twitter: {
      card: 'summary_large_image',
      title: product.title,
      description: product.shortDesc || product.desc.substring(0, 160),
      images: [product.thumbnail || product.images[0]],
    },
  };
}

export default async function ProductPage({ params }) {
  const product = await getProduct(params.slug);
  
  if (!product) {
    notFound();
  }

  const relatedProducts = await getRelatedProducts(
    product._id, 
    product.category, 
    product.brand
  );

  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Products', href: '/products' },
    { label: product.category[0], href: `/collections/${product.category[0].toLowerCase()}` },
    { label: product.title, href: `/products/${product.slug}`, current: true },
  ];

  return (
    <div className="min-h-screen bg-white">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb items={breadcrumbItems} />

        {/* Product Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Product Images */}
          <ProductImageGallery 
            images={product.images}
            title={product.title}
            thumbnail={product.thumbnail}
          />

          {/* Product Information */}
          <ProductInfo product={product} />
        </div>

        {/* Product Description & Details */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
          <div className="lg:col-span-2">
            <div className="prose max-w-none">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">Description</h2>
              <p className="text-gray-700 leading-relaxed mb-6">{product.desc}</p>
              
              {product.directions && (
                <>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">How to Use</h3>
                  <p className="text-gray-700 leading-relaxed mb-6">{product.directions}</p>
                </>
              )}
              
              {product.ingredients && (
                <>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">Ingredients</h3>
                  <p className="text-gray-700 leading-relaxed">{product.ingredients}</p>
                </>
              )}
            </div>
          </div>

          {/* Product Specifications */}
          <div className="bg-gray-50 p-6 rounded-lg h-fit">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Product Details</h3>
            <dl className="space-y-3">
              <div>
                <dt className="text-sm font-medium text-gray-500">Brand</dt>
                <dd className="text-sm text-gray-900">{product.brand}</dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-gray-500">Category</dt>
                <dd className="text-sm text-gray-900">{product.category.join(', ')}</dd>
              </div>
              {product.skinType && product.skinType.length > 0 && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Skin Type</dt>
                  <dd className="text-sm text-gray-900">{product.skinType.join(', ')}</dd>
                </div>
              )}
              {product.skinConcern && product.skinConcern.length > 0 && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Skin Concerns</dt>
                  <dd className="text-sm text-gray-900">{product.skinConcern.join(', ')}</dd>
                </div>
              )}
              {product.weight && (
                <div>
                  <dt className="text-sm font-medium text-gray-500">Weight</dt>
                  <dd className="text-sm text-gray-900">{product.weight}</dd>
                </div>
              )}
            </dl>
          </div>
        </div>

        {/* Reviews Section */}
        <ProductReviews 
          productId={product._id}
          reviews={product.reviews}
          rating={product.rating}
          ratingCount={product.ratingCount}
        />

        {/* Related Products */}
        <RelatedProducts 
          products={relatedProducts}
          title="You might also like"
        />
      </div>
    </div>
  );
}
