import { connectToDb } from "@/libs/mongoDb/connectToDbAmieShop";
import { Cart } from "@/libs/mongoDb/Models/Cart";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{params}) {
    const {id}=await params
    // console.log(id)
    await connectToDbAmieShop()
    try {
        const user= await Cart.findById(id)
        return NextResponse.json(user,{status:201})
    } catch (error) {
        return NextResponse.json('failed to find cart entry',{status:501})
    }
}
export async function PUT(req,{params}) {
    const {id}=await params
    const body=await req.json()
    // console.log(body)
    await connectToDbAmieShop()
    try {
        await Cart.findByIdAndUpdate(id,body)
        return NextResponse.json('cart entry updated',{status:201})
    } catch (error) {
        return NextResponse.json('failed to update cart entry',{status:501})
    }
}

export async function DELETE(request,{params}) {
    const {id}=await params
    console.log(id)
    await connectToDbAmieShop()
    try {
        await Cart.findByIdAndDelete(id)
        return NextResponse.json('cart entry deleted', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete cart entry", { status: 501 });
    }
}