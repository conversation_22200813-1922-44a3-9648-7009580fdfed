import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { User } from '@/libs/mongoDb/Models/User';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { skinProfile } = await request.json();

    if (!skinProfile) {
      return NextResponse.json(
        { error: 'Skin profile data is required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Update user's skin profile
    const updatedUser = await User.findByIdAndUpdate(
      session.user.id,
      { 
        skinProfile: {
          skinType: skinProfile.skinType || '',
          skinConcerns: skinProfile.skinConcerns || [],
          ageRange: skinProfile.ageRange || '',
          allergies: skinProfile.allergies || '',
          currentRoutine: skinProfile.currentRoutine || '',
          goals: skinProfile.goals || '',
          preferences: {
            naturalProducts: skinProfile.preferences?.naturalProducts || false,
            fraganceFree: skinProfile.preferences?.fraganceFree || false,
            crueltyfree: skinProfile.preferences?.crueltyfree || false,
            vegan: skinProfile.preferences?.vegan || false,
          },
          updatedAt: new Date(),
        }
      },
      { new: true, runValidators: true }
    );

    if (!updatedUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Skin profile updated successfully',
      skinProfile: updatedUser.skinProfile
    });

  } catch (error) {
    console.error('Skin profile update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
