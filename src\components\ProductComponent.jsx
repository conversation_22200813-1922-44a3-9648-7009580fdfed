'use client'
import { ACTIONS_CART } from '@/lib/amieshop/context/reducerCart'
import { useCartContext } from '@/lib/amieshop/context/useCartContext'
// import { useSession } from 'next-auth/react'
import Image from 'next/image'
import { useSearchParams } from 'next/navigation'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { FaHeart, FaRegHeart } from 'react-icons/fa6'
import { MdOutlineShoppingCart } from 'react-icons/md'

export default function ProductComponent({data}) {
  // const {data:session,status}=useSession()
  const router=useRouter()
  const {cartState,cartDispatch}=useCartContext()
  const [scrollIndex,setScrollIndex]=useState(0)
  const [count,setCount]=useState(1)
  const [tabShow,setTabShow]=useState(0)
  const searchParams=useSearchParams() 
  const query=searchParams.getAll('q')
  const infoTab=[
    'details',
    'directions',
    'ingredients',
  ]
  const images=[
    '/Frontend_Assets/product_rt_1.png',
    '/Frontend_Assets/product_rt_2.png',
    '/Frontend_Assets/product_rt_3.png',
    '/Frontend_Assets/product_rt_4.png',
  ]
  const handleAddToWishlist = () => {
    // console.log('hadle wishlist')
    cartDispatch({type:ACTIONS_CART.TOGGLE_WISHLIST})
  }
  
  const handleCount = (type) => {
    // console.log('handleCount:-',type)
    type=='-' ? setCount(prev=>prev>1 ? prev-1 : 1 ) : setCount(prev=>prev>0 ? prev+1 : 1 ) 
  }
  
  const handleAddToCart = () => {
    cartDispatch({type:ACTIONS_CART.TOGGLE_CART})
  }
  
  console.log('ProductComponent:-',cartState,cartDispatch,data)
  return (
    <div className='featuredProducts mb-20 flex flex-col md:flex-row relative w-full h-fit p-2 lg:gap-5 gap-2'>
      <div className='flex relative h-full flex-[1.5] gap-4'>
        <div className='flex flex-col relative w-1/4 h-[40svh] md:h-[75svh] items-start gap-4'>
          {data?.images.map((item,index)=>
            <div onClick={()=>setScrollIndex(index)} key={index} className='flex cursor-pointer relative w-full md:h-40 h-20 items-center'>
              {/* <div className='flex z-10 absolute top-0 left-0 bg-black/50 hover:bg-transparent duration-300 ease-linear'/> */}
              <Image className='object-cover' src={item?.image} alt='prodcut images' fill/>
            </div>)}
        </div>
        <div className='flex relative w-3/4 h-[40svh] md:h-[75svh] items-center justify-center gap-4'>
          <Image className='object-cover' src={data?.images?.[scrollIndex]?.image} alt='prodcut images' fill/>
        </div>
      </div>
      <div className='flex relative flex-col h-fit flex-[1] gap-4 pr-5'>
        <div className='flex h-fit relative w-full flex-col gap-1 md:gap-2'>
          <h1 className='flex items-center md:text-4xl text-3xl capitalize font-medium'>{data?.title}</h1>
          <span className='flex items-center text-blue-300 text-2xl md:text-5xl'>${data?.price}</span>
          <span className='text-justify font-light'>{data?.desc}</span>
          <div className='flex relative w-full h-fit gap-4'>
            <div onClick={()=>handleCount('-')} className='flex w-8 select-none h-8 rounded shadow bg-gray-200 cursor-pointer text-gray-600 items-center justify-center'>-</div>
            <span className='text-xl font-light'>{count}</span>
            <div onClick={()=>handleCount('+')} className='flex w-8 select-none h-8 rounded shadow bg-gray-200 cursor-pointer text-gray-600 items-center justify-center'>+</div>
          </div>
          <div className='flex relative items-center text-white mt-4 justify-center h-fit gap-4 rounded shadow bg-blue-500 cursor-pointer w-max px-20'>
            <MdOutlineShoppingCart className='w-4 h-4' />
            <button 
              onClick={handleAddToCart} 
              className='flex w-fit h-10 items-center select-none justify-center uppercase font-medium'
            >buy now</button>
          </div>
          <div onClick={handleAddToWishlist} className='flex relative items-center select-none h-fit gap-4 mt-4 text-blue-300 cursor-pointer w-max'>
            <FaRegHeart className='w-5 h-5' />
            <span className='text-lg uppercase'>add to wishlist</span>
          </div>
        </div>
        <div className='flex flex-col w-full h-fit gap-2 bg-gray-100'>
          <ul className='flex md:w-fit w-full h-12 gap-2 bg-gray-100 p-2'>
            {infoTab.map((tab,index)=><li key={index} onClick={()=>setTabShow(index)} className={`flex h-10 md:w-40 w-1/3 text-xs md:text-base items-center justify-center uppercase`}>
              <div className={`flex w-full h-full ${index==tabShow ? `bg-gray-100` : 'bg-white'} w-full items-center cursor-pointer select-none justify-center`}>
                {tab}
              </div>
            </li>)}
          </ul>
          <div className='flex relative w-full h-full'>
            <div className={`${tabShow==0 ? 'flex bg-gray-100 h-fit p-2' : 'hidden'} flex-col gap-4 text-justify items-center absolute left-0 top-0 text-sm w-full `}>
              <div className='flex items-center gap-2 w-full'>
                <span className='flex text-lg capitalize'>vender:</span>
                {data?.category?.map((tag,index)=>
                    <span key={index}>{tag?.item}</span>
                  )}
              </div>
              <div className='flex items-center gap-2 w-full'>
                <span className='flex text-lg capitalize'>Brand:</span>
                <span className='flex text-lg capitalize'>{data?.brand}</span>
              </div>
              <div className='flex items-center gap-2 w-full'>
                <span className='flex text-lg capitalize'>tag:</span>
                <div className='flex w-full h-fit capitalize'>
                  {data?.tags?.map((tag,index)=>
                    <span key={index}>{tag?.item},</span>
                  )}
                </div>
              </div>
            </div>
            <div className={`${tabShow==1 ? 'flex bg-gray-100 h-fit p-2' : 'hidden'} flex-col gap-4 text-justify items-center absolute left-0 top-0 text-sm w-full `}>
              {data?.directions}
            </div>
            <div className={`${tabShow==2 ? 'flex bg-gray-100 h-fit p-2' : 'hidden'} flex-col gap-4 text-justify items-center absolute left-0 top-0 text-sm w-full `}>
              {data?.ingredients}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
