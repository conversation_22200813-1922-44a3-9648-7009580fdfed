import { Order } from '@/libs/mongoDb/Models/Order';
import { Product } from '@/libs/mongoDb/Models/Products';
import { User } from '@/libs/mongoDb/Models/User';
import { sendEmail } from '@/libs/email/emailService';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

// Order fulfillment workflow
export class OrderFulfillmentService {
  
  // Process confirmed orders
  static async processConfirmedOrders() {
    try {
      await connectToDbAmieShop();
      
      // Find confirmed orders that haven't been processed
      const orders = await Order.find({
        status: 'confirmed',
        paymentStatus: 'paid',
        processedAt: { $exists: false }
      }).populate('userId', 'name email');

      for (const order of orders) {
        await this.processOrder(order);
      }

      console.log(`Processed ${orders.length} confirmed orders`);
      return orders.length;

    } catch (error) {
      console.error('Error processing confirmed orders:', error);
      throw error;
    }
  }

  // Process individual order
  static async processOrder(order) {
    try {
      // Check inventory availability
      const inventoryCheck = await this.checkInventoryAvailability(order.items);
      
      if (!inventoryCheck.available) {
        await this.handleInsufficientInventory(order, inventoryCheck.unavailableItems);
        return;
      }

      // Reserve inventory
      await this.reserveInventory(order.items);

      // Update order status
      order.status = 'processing';
      order.processedAt = new Date();
      await order.save();

      // Generate picking list
      await this.generatePickingList(order);

      // Schedule shipping label generation
      await this.scheduleShippingLabel(order);

      // Send processing notification
      if (order.userId) {
        await this.sendProcessingNotification(order);
      }

      console.log(`Order ${order.orderNumber} processed successfully`);

    } catch (error) {
      console.error(`Error processing order ${order.orderNumber}:`, error);
      
      // Mark order as failed processing
      order.status = 'processing_failed';
      order.processingError = error.message;
      await order.save();
    }
  }

  // Check inventory availability
  static async checkInventoryAvailability(items) {
    const unavailableItems = [];
    let available = true;

    for (const item of items) {
      const product = await Product.findById(item.productId);
      
      if (!product || product.stock < item.quantity) {
        unavailableItems.push({
          productId: item.productId,
          title: item.title,
          requestedQuantity: item.quantity,
          availableQuantity: product ? product.stock : 0
        });
        available = false;
      }
    }

    return { available, unavailableItems };
  }

  // Reserve inventory
  static async reserveInventory(items) {
    for (const item of items) {
      await Product.findByIdAndUpdate(
        item.productId,
        { 
          $inc: { 
            stock: -item.quantity,
            reservedStock: item.quantity 
          }
        }
      );
    }
  }

  // Handle insufficient inventory
  static async handleInsufficientInventory(order, unavailableItems) {
    order.status = 'inventory_shortage';
    order.inventoryShortage = unavailableItems;
    await order.save();

    // Send inventory shortage notification
    if (order.userId) {
      await sendEmail(order.email, 'inventoryShortage', {
        order,
        user: order.userId,
        unavailableItems
      });
    }

    // Notify admin
    console.log(`Inventory shortage for order ${order.orderNumber}:`, unavailableItems);
  }

  // Generate picking list
  static async generatePickingList(order) {
    const pickingList = {
      orderId: order._id,
      orderNumber: order.orderNumber,
      customerName: `${order.shippingAddress.firstName} ${order.shippingAddress.lastName}`,
      items: order.items.map(item => ({
        sku: item.sku,
        title: item.title,
        quantity: item.quantity,
        location: item.warehouseLocation || 'A1-01' // Default location
      })),
      priority: order.shippingMethod === 'express' ? 'high' : 'normal',
      generatedAt: new Date()
    };

    // Store picking list in order
    order.pickingList = pickingList;
    order.pickingListGeneratedAt = new Date();
    await order.save();

    return pickingList;
  }

  // Schedule shipping label generation
  static async scheduleShippingLabel(order) {
    // This would integrate with shipping providers like DHL, FedEx, etc.
    // For now, we'll simulate the process
    
    const shippingLabel = {
      orderId: order._id,
      carrier: this.selectCarrier(order),
      service: order.shippingMethod || 'standard',
      trackingNumber: this.generateTrackingNumber(),
      labelUrl: null, // Would be generated by shipping provider
      cost: order.shippingCost,
      scheduledAt: new Date()
    };

    order.shippingLabel = shippingLabel;
    order.trackingNumber = shippingLabel.trackingNumber;
    await order.save();

    return shippingLabel;
  }

  // Select shipping carrier based on order details
  static selectCarrier(order) {
    const { country, state } = order.shippingAddress;
    
    // Local delivery in Botswana
    if (country === 'Botswana') {
      return 'Local Courier';
    }
    
    // International shipping
    if (['South Africa', 'Namibia', 'Zimbabwe'].includes(country)) {
      return 'DHL Express';
    }
    
    return 'FedEx International';
  }

  // Generate tracking number
  static generateTrackingNumber() {
    const prefix = 'SA';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  // Send processing notification
  static async sendProcessingNotification(order) {
    try {
      await sendEmail(order.email, 'orderProcessing', {
        order,
        user: order.userId
      });
    } catch (error) {
      console.error('Error sending processing notification:', error);
    }
  }

  // Mark order as shipped
  static async markOrderAsShipped(orderId, trackingInfo = {}) {
    try {
      await connectToDbAmieShop();
      
      const order = await Order.findById(orderId).populate('userId', 'name email');
      
      if (!order) {
        throw new Error('Order not found');
      }

      // Update order status
      order.status = 'shipped';
      order.shippedAt = new Date();
      
      if (trackingInfo.trackingNumber) {
        order.trackingNumber = trackingInfo.trackingNumber;
      }
      
      if (trackingInfo.carrier) {
        order.carrier = trackingInfo.carrier;
      }

      await order.save();

      // Send shipping notification
      if (order.userId) {
        await sendEmail(order.email, 'shippingUpdate', {
          order,
          user: order.userId
        });
      }

      console.log(`Order ${order.orderNumber} marked as shipped`);
      return order;

    } catch (error) {
      console.error('Error marking order as shipped:', error);
      throw error;
    }
  }

  // Mark order as delivered
  static async markOrderAsDelivered(orderId) {
    try {
      await connectToDbAmieShop();
      
      const order = await Order.findById(orderId).populate('userId', 'name email');
      
      if (!order) {
        throw new Error('Order not found');
      }

      // Update order status
      order.status = 'delivered';
      order.deliveredAt = new Date();
      await order.save();

      // Send delivery confirmation
      if (order.userId) {
        await sendEmail(order.email, 'deliveryConfirmation', {
          order,
          user: order.userId
        });
      }

      console.log(`Order ${order.orderNumber} marked as delivered`);
      return order;

    } catch (error) {
      console.error('Error marking order as delivered:', error);
      throw error;
    }
  }

  // Handle order cancellation
  static async cancelOrder(orderId, reason = 'Customer request') {
    try {
      await connectToDbAmieShop();
      
      const order = await Order.findById(orderId);
      
      if (!order) {
        throw new Error('Order not found');
      }

      // Can only cancel orders that haven't shipped
      if (['shipped', 'delivered'].includes(order.status)) {
        throw new Error('Cannot cancel shipped or delivered orders');
      }

      // Restore inventory if it was reserved
      if (order.status === 'processing') {
        await this.restoreInventory(order.items);
      }

      // Update order status
      order.status = 'cancelled';
      order.cancelledAt = new Date();
      order.cancellationReason = reason;
      await order.save();

      // Process refund if payment was made
      if (order.paymentStatus === 'paid') {
        await this.processRefund(order);
      }

      console.log(`Order ${order.orderNumber} cancelled: ${reason}`);
      return order;

    } catch (error) {
      console.error('Error cancelling order:', error);
      throw error;
    }
  }

  // Restore inventory
  static async restoreInventory(items) {
    for (const item of items) {
      await Product.findByIdAndUpdate(
        item.productId,
        { 
          $inc: { 
            stock: item.quantity,
            reservedStock: -item.quantity 
          }
        }
      );
    }
  }

  // Process refund
  static async processRefund(order) {
    // This would integrate with Stripe for actual refunds
    // For now, we'll mark it as pending refund
    
    order.refundStatus = 'pending';
    order.refundRequestedAt = new Date();
    await order.save();

    console.log(`Refund requested for order ${order.orderNumber}`);
    // TODO: Integrate with Stripe refund API
  }
}

// Cron job function to process orders automatically
export async function processOrdersCronJob() {
  try {
    console.log('Starting order processing cron job...');
    const processedCount = await OrderFulfillmentService.processConfirmedOrders();
    console.log(`Order processing cron job completed. Processed ${processedCount} orders.`);
  } catch (error) {
    console.error('Order processing cron job failed:', error);
  }
}
