import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials"
import { NextResponse } from "next/server";
import { settings } from "../siteEcomSettimngs";

export const authOptions={
  pages: {
    signIn: '/ecommerce/login',
    // signOut: '/auth/signout',
    error: '/ecommerce/error', // Error code passed in query string as ?error=
    denied: '/ecommerce/denied', // Error code passed in query string as ?error=
    // verifyRequest: '/auth/verify-request', // (used for check email message)
    // newUser: '/auth/new-user' // New users will be directed here on first sign in (leave the property out if not of interest)
  },
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET
    }),
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        username: {}, password: {}
      },
      async authorize(credentials, req) {
        // console.log('credentials:-',credentials)
        if(!credentials) return NextResponse.json('please enter credentials',{status:501})
        try {
          const res=await fetch(`${settings.url.dev}/api/ecommerce/login`,{
            method:'POST',
            body:JSON.stringify(credentials)
          })
          const user = await res.json()
          if (res.ok && user) {
            // console.log('response from the login api',user)
            return user
          }
          // console.log('api return credentials :-',user)
          return null
        }catch (error) {
          console.log(error)
          NextResponse.json('failed to login with credentials, please enter correct credentials',{status:501})
        }
        return null
      }
    })
  ],
  callbacks: {
    // async signIn({ user, account, profile, email, credentials }) {
    //   console.log('sign entered:-',{token:token}, {account:account}, {profile:profile},{email:email},{credentials:credentials},{user:user})
    //   token
    //   console.log('sign before exit:-',{token:token}, {account:account}, {profile:profile},{email:email},{credentials:credentials},{user:user})
    //   return true
    // },
    async redirect({ url, baseUrl }) {
      // console.log('redirect entered:-',{url:url},{baseUrl:baseUrl})
      return `${baseUrl}/ecommerce`
    },
    async jwt({ token, user, account, profile, isNewUser }) {
      // console.log('jwt entered:-',{token:token}, {user:user}, {account:account}, {profile:profile}, {isNewUser:isNewUser})
      // token.accessToken = account.access_token
      if(user){
        token.name=user.username
        token.picture=user.img?.image||user.image 
        token.isAdmin=user.isAdmin
        token.role=user.role
      }
      // token.image=user.img
      // console.log('jwt before exit:-',{token:token}, {user:user}, {account:account}, {profile:profile}, {isNewUser:isNewUser})
      return token
    },
    async session({ session, user, token }) {
      // console.log('session entered:-',{token:token}, {user:user}, {session:session})
      session.user.name=token.name
      session.user.image=token.picture
      session.user.isAdmin=token.isAdmin
      session.user.role=token.role
      // console.log('session before exit:-',{token:token}, {user:user}, {session:session})
      return session
    },
  }
}