'use client'
import React, { useState } from 'react'

export default function QuantityCounter() {
    const [count,setCount]=useState(0)
  return (
    <div className='flex w-fit gap-2 h-fit'>
      <div className='flex bg-gray-200 shadow text-xl items-center rounded w-fit px-4 justify-center p-2'>
        -
      </div>
      <span className='text-3xl w-8 text-center'>{count}</span>
      <div className='flex bg-gray-200 shadow text-xl items-center rounded w-fit px-4 justify-center p-2'>
        +
      </div>
    </div>
  )
}
