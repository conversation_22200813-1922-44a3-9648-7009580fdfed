# SkincareAlert - E-commerce Platform

A modern, full-featured e-commerce platform for skincare products built with Next.js, modeled after skincarealert.co.bw. Features comprehensive authentication, payment processing, and a beautiful user interface.

## 🚀 Features

### Authentication & User Management
- **Multiple Authentication Providers:**
  - Magic link authentication via email (Nodemailer)
  - Google OAuth
  - Facebook OAuth
  - Traditional credentials login
- **Database Sessions:** MongoDB-based session management
- **User Accounts:** Profile management, order history, wishlist

### E-commerce Core
- **Product Management:** Advanced product catalog with variants, categories, and brands
- **Shopping Cart:** Persistent cart with guest and user support
- **Wishlist:** Save favorite products
- **Payment Processing:** Stripe integration for secure payments
- **Gift Cards:** Purchase and redeem gift cards
- **Order Management:** Complete order lifecycle

### UI/UX Features
- **Responsive Design:** Mobile-first approach with Tailwind CSS
- **Product Search & Filtering:** Advanced search with multiple filters
- **Newsletter Signup:** Popup with preferences and discount codes
- **Multi-level Navigation:** Comprehensive category and brand navigation
- **Product Reviews:** Customer reviews and ratings

### Technical Features
- **Next.js 15:** Latest features with App Router
- **MongoDB:** Flexible document database
- **Firebase Storage:** Image upload and management
- **Email Integration:** Transactional emails with Nodemailer
- **SEO Optimized:** Meta tags, structured data, and performance optimization

## 🛠️ Tech Stack

- **Frontend:** Next.js 15, React 19, Tailwind CSS
- **Backend:** Next.js API Routes, MongoDB with Mongoose
- **Authentication:** NextAuth.js v5 (Auth.js)
- **Payments:** Stripe
- **Storage:** Firebase Storage
- **Email:** Nodemailer
- **UI Components:** Lucide React icons, Headless UI
- **State Management:** Zustand, React Context

## 📋 Prerequisites

- Node.js 18+
- MongoDB database
- Stripe account
- Firebase project (for image storage)
- Email service (Gmail, SendGrid, etc.)
- OAuth app credentials (Google, Facebook)

## 🚀 Getting Started

### 1. Clone the Repository

```bash
git clone <repository-url>
cd amostore
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Environment Setup

Copy the example environment file and configure your variables:

```bash
cp .env.example .env.local
```

Fill in your environment variables in `.env.local`:

```env
# Database
MONGODB_URI=mongodb://localhost:27017/skincare-store

# NextAuth.js
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here

# Google OAuth
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook OAuth
FACEBOOK_CLIENT_ID=your-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-facebook-client-secret

# Email Configuration
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Stripe
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-publishable-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key

# Firebase
NEXT_PUBLIC_FIREBASE_API_KEY=your-firebase-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
```

### 4. Database Setup

Ensure MongoDB is running and accessible via your `MONGODB_URI`.

### 5. Run Development Server

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
