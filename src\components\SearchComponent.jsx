'use client'
import React, { useState } from 'react'
import { IoIosSearch } from 'react-icons/io'
import { IoClose } from 'react-icons/io5'

export default function SearchComponent() {
    const [showSearch,setShowSearch]=useState(false)
  return (
    <div  className='flex items-center w-fit h-10 relative'>
        {showSearch 
            ?   <div className='flex md:w-48 w-24 relative rounded-md border-[1px] border-gray-400 text-gray-500 items-center p-2'>
                    <input type="text" placeholder='search' className='flex items-center w-full px-2 mr-4 outline-none'/>
                    <IoIosSearch onClick={()=>setShowSearch(!showSearch)} className='flex text-xl absolute right-2'/>
                    {showSearch && <IoClose onClick={()=>setShowSearch(!showSearch)} className='flex cursor-pointer absolute right-7'/>}
                </div>
            :   <IoIosSearch onClick={()=>setShowSearch(!showSearch)} className='flex items-center justify-center rounded-full p-2 border-[1px] border-gray-400 text-4xl cursor-pointer'/> 
        }
    </div>
  )
}
