'use client';

import { useState } from 'react';
import { StarIcon, HeartIcon, ShareIcon, ShoppingCartIcon, CheckIcon } from 'lucide-react';
import { useSession } from 'next-auth/react';
import SocialShare from '@/components/SocialShare';

export default function ProductInfo({ product }) {
  const { data: session } = useSession();
  const [selectedVariant, setSelectedVariant] = useState(product.variants?.[0] || null);
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [isAddingToWishlist, setIsAddingToWishlist] = useState(false);
  const [showShare, setShowShare] = useState(false);

  const currentPrice = selectedVariant?.price || product.price;
  const salePrice = product.salePrice;
  const isOnSale = salePrice && salePrice > currentPrice;
  const discount = isOnSale ? Math.round(((salePrice - currentPrice) / salePrice) * 100) : 0;

  const handleAddToCart = async () => {
    setIsAddingToCart(true);
    try {
      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: product._id,
          variantId: selectedVariant?._id,
          quantity,
          price: currentPrice,
        }),
      });

      if (response.ok) {
        // Show success message or update cart state
        console.log('Added to cart successfully');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsAddingToCart(false);
    }
  };

  const handleAddToWishlist = async () => {
    if (!session) {
      // Redirect to login or show login modal
      return;
    }

    setIsAddingToWishlist(true);
    try {
      const response = await fetch('/api/wishlist/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: product._id,
        }),
      });

      if (response.ok) {
        console.log('Added to wishlist successfully');
      }
    } catch (error) {
      console.error('Error adding to wishlist:', error);
    } finally {
      setIsAddingToWishlist(false);
    }
  };

  const renderStars = (rating) => {
    return (
      <div className="flex items-center">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIcon
            key={star}
            className={`w-5 h-5 ${
              star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Brand */}
      <div className="text-sm text-gray-500 uppercase tracking-wide">
        {product.brand}
      </div>

      {/* Title */}
      <h1 className="text-3xl font-bold text-gray-900">{product.title}</h1>

      {/* Rating */}
      {product.ratingCount > 0 && (
        <div className="flex items-center space-x-2">
          {renderStars(product.rating)}
          <span className="text-sm text-gray-600">
            ({product.ratingCount} review{product.ratingCount !== 1 ? 's' : ''})
          </span>
        </div>
      )}

      {/* Price */}
      <div className="flex items-center space-x-3">
        <span className="text-3xl font-bold text-gray-900">
          P{currentPrice.toFixed(2)}
        </span>
        {isOnSale && (
          <>
            <span className="text-xl text-gray-500 line-through">
              P{salePrice.toFixed(2)}
            </span>
            <span className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm font-medium">
              {discount}% OFF
            </span>
          </>
        )}
      </div>

      {/* Short Description */}
      {product.shortDesc && (
        <p className="text-gray-700 leading-relaxed">{product.shortDesc}</p>
      )}

      {/* Variants */}
      {product.variants && product.variants.length > 0 && (
        <div className="space-y-4">
          {/* Size Selection */}
          {product.variants.some(v => v.size) && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Size
              </label>
              <div className="flex flex-wrap gap-2">
                {product.variants
                  .filter(v => v.size)
                  .map((variant) => (
                    <button
                      key={variant._id}
                      onClick={() => setSelectedVariant(variant)}
                      className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
                        selectedVariant?._id === variant._id
                          ? 'border-gray-900 bg-gray-900 text-white'
                          : 'border-gray-300 text-gray-700 hover:border-gray-400'
                      }`}
                    >
                      {variant.size}
                    </button>
                  ))}
              </div>
            </div>
          )}

          {/* Color Selection */}
          {product.variants.some(v => v.color) && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Color
              </label>
              <div className="flex flex-wrap gap-2">
                {product.variants
                  .filter(v => v.color)
                  .map((variant) => (
                    <button
                      key={variant._id}
                      onClick={() => setSelectedVariant(variant)}
                      className={`px-4 py-2 border rounded-lg text-sm font-medium transition-colors ${
                        selectedVariant?._id === variant._id
                          ? 'border-gray-900 bg-gray-900 text-white'
                          : 'border-gray-300 text-gray-700 hover:border-gray-400'
                      }`}
                    >
                      {variant.color}
                    </button>
                  ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Quantity */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Quantity
        </label>
        <div className="flex items-center space-x-3">
          <button
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            className="w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
          >
            -
          </button>
          <span className="w-12 text-center font-medium">{quantity}</span>
          <button
            onClick={() => setQuantity(quantity + 1)}
            className="w-10 h-10 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
          >
            +
          </button>
        </div>
      </div>

      {/* Stock Status */}
      <div className="flex items-center space-x-2">
        {product.inStock ? (
          <>
            <CheckIcon className="w-5 h-5 text-green-500" />
            <span className="text-green-600 font-medium">In Stock</span>
          </>
        ) : (
          <span className="text-red-600 font-medium">Out of Stock</span>
        )}
      </div>

      {/* Action Buttons */}
      <div className="space-y-3">
        <button
          onClick={handleAddToCart}
          disabled={!product.inStock || isAddingToCart}
          className="w-full bg-gray-900 text-white py-4 px-6 rounded-lg font-semibold hover:bg-gray-800 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
        >
          <ShoppingCartIcon className="w-5 h-5" />
          <span>
            {isAddingToCart ? 'Adding...' : 'Add to Cart'}
          </span>
        </button>

        <div className="flex space-x-3">
          <button
            onClick={handleAddToWishlist}
            disabled={isAddingToWishlist}
            className="flex-1 border border-gray-300 text-gray-700 py-3 px-6 rounded-lg font-medium hover:bg-gray-50 transition-colors flex items-center justify-center space-x-2"
          >
            <HeartIcon className="w-5 h-5" />
            <span>
              {isAddingToWishlist ? 'Adding...' : 'Add to Wishlist'}
            </span>
          </button>

          <button
            onClick={() => setShowShare(!showShare)}
            className="border border-gray-300 text-gray-700 py-3 px-4 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <ShareIcon className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Social Share */}
      {showShare && (
        <SocialShare
          url={typeof window !== 'undefined' ? window.location.href : ''}
          title={product.title}
          description={product.shortDesc || product.desc}
        />
      )}

      {/* Key Features */}
      {(product.skinType?.length > 0 || product.skinConcern?.length > 0) && (
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-3">Perfect For</h3>
          <div className="space-y-2">
            {product.skinType?.length > 0 && (
              <div>
                <span className="text-sm font-medium text-gray-700">Skin Types: </span>
                <span className="text-sm text-gray-600">{product.skinType.join(', ')}</span>
              </div>
            )}
            {product.skinConcern?.length > 0 && (
              <div>
                <span className="text-sm font-medium text-gray-700">Skin Concerns: </span>
                <span className="text-sm text-gray-600">{product.skinConcern.join(', ')}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
