import bcrypt from "bcrypt";
import connectToDbAmieShop from "@/libs/mongoDb/connectToDbAmieShop";
import { User } from "@/libs/mongoDb/Models/User";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function POST(req) {
    const body=await req.json()
    console.log(body)
    await connectToDbAmieShop()
    try {
        // CHECK IF CREDENTIALS ARE VALID
        if(!body.password || !body.email) return NextResponse.json('please provide crendetials',{status:501})
            
        // CHECK IF USER EXITS IN DB
        const userExits=await User.findOne({$or:[{email:body.email},{username:body.username}]})
        if(userExits) return NextResponse.json('please enter unique credentials',{status:501})
        // console.log(body)
    
        // HASH USER PASSWORD
        const hashPassword = bcrypt.hashSync(body.password, 10)
        body.password=hashPassword
        
        // ADD USER TO DB
        // console.log(body)
        const newUser=await new User(body)
        newUser.save()
        return NextResponse.json('successfully registered user', { status: 201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to register user',{status:501})
    }
}

