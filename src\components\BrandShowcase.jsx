'use client';

import { useState } from 'react';
import Link from 'next/link';

const brands = [
  { id: 1, name: "Fundamentals", slug: "fundamentals-skincare", logo: "/images/brands/fundamentals.jpg" },
  { id: 2, name: "Lanolab", slug: "lanolab", logo: "/images/brands/lanolab.jpg" },
  { id: 3, name: "Morite<PERSON>", slug: "moritelo", logo: "/images/brands/moritelo.jpg" },
  { id: 4, name: "Nakolwethu", slug: "nakolwethu", logo: "/images/brands/nakolwethu.jpg" },
  { id: 5, name: "Organic Naturals", slug: "organic-naturals", logo: "/images/brands/organic-naturals.jpg" },
  { id: 6, name: "Perile", slug: "perile", logo: "/images/brands/perile.jpg" },
  { id: 7, name: "<PERSON><PERSON>", slug: "silki", logo: "/images/brands/silki.jpg" },
  { id: 8, name: "Skinbliss", slug: "skinbliss", logo: "/images/brands/skinbliss.jpg" },
  { id: 9, name: "SKIN Functional", slug: "skin-functional", logo: "/images/brands/skin-functional.jpg" },
  { id: 10, name: "Skinstem", slug: "skinstem", logo: "/images/brands/skinstem.jpg" },
  { id: 11, name: "Standard Beauty", slug: "standard-beauty", logo: "/images/brands/standard-beauty.jpg" },
  { id: 12, name: "Your Only One", slug: "your-only-one", logo: "/images/brands/your-only-one.jpg" }
];

export default function BrandShowcase() {
  const [hoveredBrand, setHoveredBrand] = useState(null);

  return (
    <section className="py-16 bg-gray-50">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Our Trusted Brands
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover effective and affordable skincare from brands we trust and love
          </p>
        </div>

        {/* Brands Grid */}
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6 mb-12">
          {brands.map((brand) => (
            <Link
              key={brand.id}
              href={`/brands/${brand.slug}`}
              className="group"
              onMouseEnter={() => setHoveredBrand(brand.id)}
              onMouseLeave={() => setHoveredBrand(null)}
            >
              <div className="bg-white rounded-lg p-6 shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
                {/* Brand Logo Placeholder */}
                <div className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center mb-3 overflow-hidden">
                  <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                    <span className="text-xs text-gray-500 text-center px-2">
                      {brand.name}
                    </span>
                  </div>
                </div>
                
                {/* Brand Name */}
                <h3 className="text-sm font-medium text-gray-900 text-center group-hover:text-blue-600 transition-colors">
                  {brand.name}
                </h3>
              </div>
            </Link>
          ))}
        </div>

        {/* Featured Brand Spotlight */}
        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div className="space-y-6">
              <div className="space-y-4">
                <span className="inline-block bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                  Featured Brand
                </span>
                <h3 className="text-3xl font-bold text-gray-900">
                  Discover Fundamentals Skincare
                </h3>
                <p className="text-lg text-gray-600">
                  Experience the power of natural ingredients with Fundamentals Skincare. 
                  From their popular Baobab Milk Toner to innovative serums, discover 
                  products that work with your skin, not against it.
                </p>
              </div>
              
              <div className="flex flex-wrap gap-4">
                <Link
                  href="/brands/fundamentals-skincare"
                  className="bg-gray-900 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-800 transition-colors"
                >
                  Shop Fundamentals
                </Link>
                <Link
                  href="/brands"
                  className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors"
                >
                  View All Brands
                </Link>
              </div>
            </div>

            {/* Featured Brand Image */}
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-blue-100 to-purple-100 rounded-2xl flex items-center justify-center">
                <span className="text-gray-500">Featured Brand Image</span>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-white rounded-full p-4 shadow-lg">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">✓</span>
                </div>
              </div>
              
              <div className="absolute -bottom-4 -left-4 bg-white rounded-lg p-3 shadow-lg">
                <div className="text-xs text-gray-600">
                  <div className="font-semibold">Natural</div>
                  <div>Ingredients</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
