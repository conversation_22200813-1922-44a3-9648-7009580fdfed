import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Cart } from '@/libs/mongoDb/Models/Cart';
import { Product } from '@/libs/mongoDb/Models/Products';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    const { productId, variantId, quantity = 1, price } = await request.json();

    if (!productId || !price) {
      return NextResponse.json(
        { error: 'Product ID and price are required' },
        { status: 400 }
      );
    }

    await connectToDbAmieShop();

    // Verify product exists and is active
    const product = await Product.findById(productId);
    if (!product || product.status !== 'active') {
      return NextResponse.json(
        { error: 'Product not found or unavailable' },
        { status: 404 }
      );
    }

    let cart;
    
    if (session?.user) {
      // Authenticated user
      cart = await Cart.findOne({ userId: session.user.id });
      
      if (!cart) {
        cart = new Cart({
          userId: session.user.id,
          items: [],
        });
      }
    } else {
      // Guest user - would need session ID from cookies
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex(
      item => 
        item.productId.toString() === productId && 
        item.variantId === variantId
    );

    if (existingItemIndex > -1) {
      // Update quantity
      cart.items[existingItemIndex].quantity += quantity;
    } else {
      // Add new item
      cart.items.push({
        productId,
        variantId,
        quantity,
        price,
      });
    }

    await cart.save();

    return NextResponse.json({
      message: 'Item added to cart successfully',
      cart,
    });

  } catch (error) {
    console.error('Add to cart error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
