import connectToDbAmieShop from "@/libs/mongoDb/connectToDbAmieShop";
import { Order } from "@/libs/mongoDb/Models/Order";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{searchParams}) {
    // const filters = await searchParams
    // console.log(filters)
    await connectToDbAmieShop()
    try {
        const orders= await Order.find()
        return NextResponse.json(orders,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to get orders',{status:501})
    }
}

export async function POST(req) {
    const body=await req.json()
    // console.log(body)
    await connectToDbAmieShop()
    try {
        const newOrder=await Order(body)
        newOrder.save()
        return NextResponse.json(newOrder,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to update order details',{status:501})
    }
}

export async function DELETE(request) {
    // console.log(request.json())
    await connectToDbAmieShop()
    try {
        await Order.deleteMany()
        return NextResponse.json('deleted all users from database', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete all users from database", { status: 501 });
    }
}