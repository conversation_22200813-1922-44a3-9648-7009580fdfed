'use client';

import { useState, useEffect } from 'react';
import { MarsIcon, PanelTopIcon, GiftIcon } from 'lucide-react';

export default function NewsletterPopup() {
  const [isVisible, setIsVisible] = useState(false);
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  useEffect(() => {
    // Check if user has already subscribed or dismissed
    const hasSubscribed = localStorage.getItem('newsletter_subscribed');
    const hasDismissed = localStorage.getItem('newsletter_dismissed');
    
    if (!hasSubscribed && !hasDismissed) {
      // Show popup after 10 seconds of browsing
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 10000);

      return () => clearTimeout(timer);
    }
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch('/api/newsletter/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, source: 'popup' }),
      });

      if (response.ok) {
        setIsSubmitted(true);
        localStorage.setItem('newsletter_subscribed', 'true');
        
        // Close popup after 3 seconds
        setTimeout(() => {
          setIsVisible(false);
        }, 3000);
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setIsVisible(false);
    localStorage.setItem('newsletter_dismissed', 'true');
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-md w-full relative overflow-hidden shadow-2xl">
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600 z-10"
        >
          <MarsIcon className="w-6 h-6" />
        </button>

        {/* Header with Gradient */}
        <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white p-6 text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-white/20 p-3 rounded-full">
              <PanelTopIcon className="w-8 h-8" />
            </div>
          </div>
          <h2 className="text-2xl font-bold mb-2">
            Get Skincare Tips & Exclusive Offers!
          </h2>
          <p className="text-pink-100">
            Join our community for expert skincare advice and special discounts
          </p>
        </div>

        {/* Content */}
        <div className="p-6">
          {!isSubmitted ? (
            <>
              {/* Benefits */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-1 rounded-full">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="text-sm text-gray-700">Early access to new products</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-1 rounded-full">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="text-sm text-gray-700">Exclusive skincare tips & routines</span>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-1 rounded-full">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="text-sm text-gray-700">Special member-only discounts</span>
                </div>
              </div>

              {/* Special Offer */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-2 mb-2">
                  <GiftIcon className="w-5 h-5 text-yellow-600" />
                  <span className="font-semibold text-yellow-800">Special Offer</span>
                </div>
                <p className="text-sm text-yellow-700">
                  Get 10% off your first order when you subscribe!
                </p>
              </div>

              {/* Email Form */}
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  />
                </div>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-pink-600 hover:to-purple-700 transition-all duration-300 disabled:opacity-50"
                >
                  {isSubmitting ? 'Subscribing...' : 'Subscribe & Get 10% Off'}
                </button>
              </form>

              <p className="text-xs text-gray-500 text-center mt-4">
                By subscribing, you agree to our privacy policy. Unsubscribe at any time.
              </p>
            </>
          ) : (
            /* Success State */
            <div className="text-center py-8">
              <div className="bg-green-100 p-4 rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-lg font-bold">✓</span>
                </div>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Welcome to our community!
              </h3>
              <p className="text-gray-600 mb-4">
                Check your email for your 10% discount code and skincare tips.
              </p>
              <p className="text-sm text-gray-500">
                This popup will close automatically...
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
