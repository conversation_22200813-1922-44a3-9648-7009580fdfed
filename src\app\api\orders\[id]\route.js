import { connectToDb } from "@/lib/mongoDb/connectToDb";
import { Order } from "@/lib/mongoDb/Models/Order";
import { User } from "@/lib/mongoDb/Models/User";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{params}) {
    const {id}=await params
    // console.log(id)
    connectToDb()
    try {
        const user= await Order.findById(id)
        return NextResponse.json(user,{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to find order',{status:501})
    }
}
export async function PUT(req,{params}) {
    const {id}=await params
    const body=await req.json()
    // console.log(body)
    connectToDb()
    try {
        await Order.findByIdAndUpdate(id,body)
        return NextResponse.json('order details updated',{status:201})
    } catch (error) {
        console.log(error)
        return NextResponse.json('failed to update order details',{status:501})
    }
}

export async function DELETE(request,{params}) {
    const {id}=await params
    console.log(id)
    await connectToDb()
    try {
        await Order.findByIdAndDelete(id)
        return NextResponse.json('order deleted', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete order", { status: 501 });
    }
}