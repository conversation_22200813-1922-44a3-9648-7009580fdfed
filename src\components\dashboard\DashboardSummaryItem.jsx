import Link from 'next/link'
import React from 'react'
import { AiOutlineUser } from 'react-icons/ai'
import { IoMdTrendingDown, IoMdTrendingUp } from 'react-icons/io'

export default function DashboardSummaryItem({data}) {
  return (
    <div className='flex w-full h-full rounded-lg bg-white shadow-lg lg:p-4 p-2'>
      <div className='flex flex-col flex-1 h-full justify-between'>
        <span className='uppercase text-sm md:text-sm lx:text-xl text-gray-300'>{data?.name}</span>
        <span className='ml-2 font-normal md:text-4xl text-2xl'>{data?.name=='users' || data?.name=='orders' ? 400 : `$${300}`}</span>
        <Link href={`/dashboard/reports`} className='underline text-xs text-wrap md:text-sm mb-1'>{'See total ' +  data?.name}</Link>
      </div>
      <div className='flex flex-col w-fit h-full items-end justify-between'>
        <div className='flex items-center w-fit'>
          {true
            ? <div className='flex text-sm items-end w-fit h-fit lg:gap-2 text-nowrap text-green-400'>
                <IoMdTrendingUp className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
            : <div className='flex items-end w-fit h-fit lg:gap-2 text-red-400'>
                <IoMdTrendingDown className='w-5 h-5'/>
                <span>{20} %</span>
              </div>
          }
        </div>
        <div className='flex items-center justify-center h-fit w-fit'>
          {data?.icon}
        </div>
      </div>
    </div>
  )
}
