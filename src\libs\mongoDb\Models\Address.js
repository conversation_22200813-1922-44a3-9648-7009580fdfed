import mongoose from 'mongoose';
const { Schema } = mongoose;

const addressSchema = new Schema({
  userId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  type: { type: String, enum: ['shipping', 'billing', 'both'], default: 'both' },
  isDefault: { type: Boolean, default: false },
  
  // Personal Information
  firstName: { type: String, required: true, trim: true },
  lastName: { type: String, required: true, trim: true },
  company: { type: String, trim: true },
  
  // Address Information
  address1: { type: String, required: true, trim: true },
  address2: { type: String, trim: true },
  city: { type: String, required: true, trim: true },
  state: { type: String, required: true, trim: true },
  postalCode: { type: String, required: true, trim: true },
  country: { type: String, required: true, trim: true, default: 'Botswana' },
  
  // Contact Information
  phone: { type: String, trim: true },
  
  // Validation
  isValidated: { type: Boolean, default: false },
  validationDate: { type: Date },
  
  // Metadata
  label: { type: String, trim: true }, // e.g., "Home", "Office", "Mom's House"
  instructions: { type: String, trim: true }, // Delivery instructions
}, { timestamps: true });

// Ensure only one default address per type per user
addressSchema.index({ userId: 1, type: 1, isDefault: 1 });

// Pre-save middleware to handle default address logic
addressSchema.pre('save', async function(next) {
  if (this.isDefault && this.isModified('isDefault')) {
    // Remove default flag from other addresses of the same type
    await mongoose.model('Address').updateMany(
      { 
        userId: this.userId, 
        type: this.type, 
        _id: { $ne: this._id } 
      },
      { isDefault: false }
    );
  }
  next();
});

// Static method to get default address
addressSchema.statics.getDefaultAddress = function(userId, type = 'shipping') {
  return this.findOne({ userId, type: { $in: [type, 'both'] }, isDefault: true });
};

// Instance method to set as default
addressSchema.methods.setAsDefault = async function() {
  // Remove default from others
  await this.constructor.updateMany(
    { 
      userId: this.userId, 
      type: this.type, 
      _id: { $ne: this._id } 
    },
    { isDefault: false }
  );
  
  // Set this as default
  this.isDefault = true;
  return this.save();
};

// Virtual for full name
addressSchema.virtual('fullName').get(function() {
  return `${this.firstName} ${this.lastName}`;
});

// Virtual for formatted address
addressSchema.virtual('formattedAddress').get(function() {
  const parts = [
    this.address1,
    this.address2,
    this.city,
    this.state,
    this.postalCode,
    this.country
  ].filter(Boolean);
  
  return parts.join(', ');
});

export const Address = mongoose.models.Address || mongoose.model('Address', addressSchema);
