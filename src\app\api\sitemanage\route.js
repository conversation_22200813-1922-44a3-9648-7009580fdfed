import { Site } from "@/lib/mongoDb/Models/Site";
import { connectToDbAmieShop } from "@/libs/mongoDb/connectToDbAmieShop";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req) {
    connectToDbAmieShop()
    try {
        const siteDetails= await Site.find()
        const {_id,createdAt,updatedAt,__v,...others}=siteDetails[0]._doc
        // console.log(others)
        return NextResponse.json(others,{status:201})
    } catch (error) {
        return NextResponse.json('failed to get sitedetails',{status:501})
    }
}

export async function DELETE(request) {
    // console.log(request.json())
    await connectToDbAmieShop()
    try {
        const users=await Site.deleteMany()
        return NextResponse.json('to delete site details', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete all Sites from database", { status: 501 });
    }
}

export async function PUT(req,{params}) {
    // const {id}=await params
    const body=await req.json()
    // console.log('server put:',body)
    connectToDbAmieShop()
    try {
        const res=await Site.findOneAndUpdate({shopName:body?.shopName},body,{new: true})
        //   console.log('server response:',res)
        return NextResponse.json('site details updated',{status:201})
    } catch (error) {
        return NextResponse.json('failed to update site details',{status:501})
    }
}

export async function POST(req,{params}) {
    // const {id}=await params
    const body=await req.json()
    // console.log(body)
    connectToDbAmieShop()
    try {
        const siteInfo=await Site(body)
        siteInfo.save()
        return NextResponse.json(siteInfo,{status:201})
    } catch (error) {
        return NextResponse.json('failed to update site details',{status:501})
    }
}