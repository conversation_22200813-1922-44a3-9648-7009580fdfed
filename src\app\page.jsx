import Footer from "@/components/Footer";
import ProductsComponent from "@/components/ProductsComponent";
import { settings } from "@/libs/siteEcomSettimngs";

export default async function Home() {
  // const dataDb = await fetch(`${settings.url}/api/products`)
  // const data = await dataDb.json()
    // console.log('Home:',data)
  return (
    <div className='flex relative flex-col w-full text-gray-400  h-full items-center justify-center overflow-y-auto gap-5'>
      <div className='flex md:w-10/12 w-full px-2 h-fit items-center flex-wrap'>
        {/* <CartComponent/> */}
        {/* <ProductsComponent data={data}/> */}
      </div>
      <Footer/>
    </div>
  );
}
export const dynamic = 'force-dynamic'