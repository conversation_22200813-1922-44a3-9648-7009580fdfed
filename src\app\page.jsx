import HeroSection from "@/components/HeroSection";
import FeaturedProducts from "@/components/FeaturedProducts";
import BrandShowcase from "@/components/BrandShowcase";
import ProductCategories from "@/components/ProductCategories";
import NewsletterPopup from "@/components/NewsletterPopup";
import Footer from "@/components/Footer";
import { settings } from "@/libs/siteEcomSettimngs";

export default async function Home() {
  // Fetch featured products and brands
  // const featuredProducts = await fetch(`${settings.url}/api/products?featured=true`)
  // const brands = await fetch(`${settings.url}/api/brands?featured=true`)

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <HeroSection />

      {/* Featured Products */}
      <FeaturedProducts />

      {/* Product Categories */}
      <ProductCategories />

      {/* Brand Showcase */}
      <BrandShowcase />

      {/* Newsletter Popup */}
      <NewsletterPopup />

      {/* Footer */}
      <Footer />
    </div>
  );
}

export const dynamic = 'force-dynamic';