import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import AccountLayout from '@/components/AccountLayout';
import OrdersPage from '@/components/OrdersPage';

export const metadata = {
  title: 'My Orders - SkincareAlert',
  description: 'View and manage your orders',
};

export default async function Orders() {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect('/auth/signin?callbackUrl=/account/orders');
  }

  return (
    <AccountLayout>
      <OrdersPage userId={session.user.id} />
    </AccountLayout>
  );
}
