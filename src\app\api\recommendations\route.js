import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Product } from '@/libs/mongoDb/Models/Products';
import { Order } from '@/libs/mongoDb/Models/Order';
import { Wishlist } from '@/libs/mongoDb/Models/Wishlist';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function GET(request) {
  try {
    await connectToDbAmieShop();
    
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'general';
    const productId = searchParams.get('productId');
    const skinProfile = searchParams.get('skinProfile');
    const limit = parseInt(searchParams.get('limit')) || 8;

    const session = await getServerSession(authOptions);
    let recommendations = [];

    switch (type) {
      case 'similar':
        recommendations = await getSimilarProducts(productId, limit);
        break;
      case 'frequently-bought':
        recommendations = await getFrequentlyBoughtTogether(productId, limit);
        break;
      case 'skin-profile':
        recommendations = await getSkinProfileRecommendations(skinProfile, session?.user?.id, limit);
        break;
      case 'general':
      default:
        recommendations = await getGeneralRecommendations(session?.user?.id, limit);
        break;
    }

    return NextResponse.json({ recommendations });

  } catch (error) {
    console.error('Recommendations error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

async function getSimilarProducts(productId, limit) {
  if (!productId) return [];

  const product = await Product.findById(productId);
  if (!product) return [];

  // Find products with similar categories, brand, or skin concerns
  const similarProducts = await Product.find({
    _id: { $ne: productId },
    status: 'active',
    $or: [
      { brand: product.brand },
      { category: { $in: product.category } },
      { skinType: { $in: product.skinType || [] } },
      { skinConcern: { $in: product.skinConcern || [] } }
    ]
  })
  .sort({ rating: -1, ratingCount: -1 })
  .limit(limit)
  .lean();

  return similarProducts.map(p => ({
    ...p,
    recommendationReason: 'Similar to your viewed product'
  }));
}

async function getFrequentlyBoughtTogether(productId, limit) {
  if (!productId) return [];

  // Find orders that contain this product
  const ordersWithProduct = await Order.find({
    'items.productId': productId
  }).lean();

  // Count frequency of other products in these orders
  const productFrequency = {};
  
  ordersWithProduct.forEach(order => {
    order.items.forEach(item => {
      if (item.productId.toString() !== productId) {
        const id = item.productId.toString();
        productFrequency[id] = (productFrequency[id] || 0) + 1;
      }
    });
  });

  // Get top frequently bought products
  const topProductIds = Object.entries(productFrequency)
    .sort(([,a], [,b]) => b - a)
    .slice(0, limit)
    .map(([id]) => id);

  if (topProductIds.length === 0) return [];

  const products = await Product.find({
    _id: { $in: topProductIds },
    status: 'active'
  }).lean();

  return products.map(p => ({
    ...p,
    recommendationReason: 'Frequently bought together'
  }));
}

async function getSkinProfileRecommendations(skinProfileStr, userId, limit) {
  let skinProfile = {};
  
  try {
    if (skinProfileStr) {
      skinProfile = JSON.parse(skinProfileStr);
    }
  } catch (error) {
    console.error('Error parsing skin profile:', error);
  }

  const { skinType, skinConcerns, ageRange, preferences } = skinProfile;

  // Build query based on skin profile
  let query = { status: 'active' };
  let scoreFactors = [];

  if (skinType) {
    query.skinType = { $in: [skinType] };
    scoreFactors.push({ skinType: skinType });
  }

  if (skinConcerns && skinConcerns.length > 0) {
    query.skinConcern = { $in: skinConcerns };
    scoreFactors.push({ skinConcern: { $in: skinConcerns } });
  }

  // Get user's purchase history for personalization
  let userPreferences = [];
  if (userId) {
    const userOrders = await Order.find({ userId }).lean();
    const purchasedBrands = [...new Set(userOrders.flatMap(order => 
      order.items.map(item => item.brand)
    ))];
    
    if (purchasedBrands.length > 0) {
      userPreferences = purchasedBrands;
    }
  }

  const products = await Product.find(query)
    .sort({ rating: -1, ratingCount: -1 })
    .limit(limit * 2) // Get more to filter and score
    .lean();

  // Score products based on skin profile match
  const scoredProducts = products.map(product => {
    let score = 0.5; // Base score

    // Skin type match
    if (skinType && product.skinType?.includes(skinType)) {
      score += 0.3;
    }

    // Skin concern match
    if (skinConcerns) {
      const matchingConcerns = product.skinConcern?.filter(concern => 
        skinConcerns.includes(concern)
      ) || [];
      score += (matchingConcerns.length / Math.max(skinConcerns.length, 1)) * 0.2;
    }

    // Brand preference
    if (userPreferences.includes(product.brand)) {
      score += 0.1;
    }

    // Rating boost
    score += (product.rating / 5) * 0.1;

    return {
      ...product,
      recommendationScore: Math.min(score, 1),
      skinMatch: [
        ...(skinType && product.skinType?.includes(skinType) ? [skinType] : []),
        ...(product.skinConcern?.filter(concern => 
          skinConcerns?.includes(concern)
        ) || [])
      ],
      recommendationReason: 'Matched to your skin profile'
    };
  });

  // Sort by score and return top results
  return scoredProducts
    .sort((a, b) => b.recommendationScore - a.recommendationScore)
    .slice(0, limit);
}

async function getGeneralRecommendations(userId, limit) {
  let recommendations = [];

  if (userId) {
    // Get user's browsing and purchase history
    const [userOrders, userWishlist] = await Promise.all([
      Order.find({ userId }).lean(),
      Wishlist.findOne({ userId }).lean()
    ]);

    // Extract user preferences
    const purchasedCategories = [...new Set(userOrders.flatMap(order => 
      order.items.flatMap(item => item.category || [])
    ))];
    
    const purchasedBrands = [...new Set(userOrders.flatMap(order => 
      order.items.map(item => item.brand)
    ))];

    const wishlistedProductIds = userWishlist?.items.map(item => item.productId) || [];

    // Build personalized query
    let query = { 
      status: 'active',
      _id: { $nin: wishlistedProductIds }
    };

    if (purchasedCategories.length > 0 || purchasedBrands.length > 0) {
      query.$or = [
        ...(purchasedCategories.length > 0 ? [{ category: { $in: purchasedCategories } }] : []),
        ...(purchasedBrands.length > 0 ? [{ brand: { $in: purchasedBrands } }] : [])
      ];
    }

    recommendations = await Product.find(query)
      .sort({ rating: -1, ratingCount: -1, createdAt: -1 })
      .limit(limit)
      .lean();

    // Add recommendation reasons
    recommendations = recommendations.map(p => ({
      ...p,
      recommendationReason: 'Based on your preferences'
    }));
  }

  // If no personalized recommendations or not enough, add popular products
  if (recommendations.length < limit) {
    const popularProducts = await Product.find({
      status: 'active',
      _id: { $nin: recommendations.map(p => p._id) }
    })
    .sort({ rating: -1, ratingCount: -1, featured: -1 })
    .limit(limit - recommendations.length)
    .lean();

    const popularWithReason = popularProducts.map(p => ({
      ...p,
      recommendationReason: 'Popular choice'
    }));

    recommendations = [...recommendations, ...popularWithReason];
  }

  return recommendations.slice(0, limit);
}
