import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/libs/authOptions';
import { Address } from '@/libs/mongoDb/Models/Address';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

export async function POST(request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const addressData = await request.json();

    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'address1', 'city', 'state', 'postalCode', 'country'];
    for (const field of requiredFields) {
      if (!addressData[field]?.trim()) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    await connectToDbAmieShop();

    // If this is set as default, ensure no other address of the same type is default
    if (addressData.isDefault) {
      await Address.updateMany(
        { 
          userId: session.user.id, 
          type: addressData.type 
        },
        { isDefault: false }
      );
    }

    // Create new address
    const newAddress = new Address({
      ...addressData,
      userId: session.user.id,
    });

    await newAddress.save();

    return NextResponse.json({
      message: 'Address created successfully',
      address: newAddress
    }, { status: 201 });

  } catch (error) {
    console.error('Address creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
