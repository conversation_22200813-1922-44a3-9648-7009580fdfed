import { connectToDbAmieShop } from "@/lib/amieshop/mongoDb/connectToDbAmieShop";
import { Product } from "@/lib/amieshop/mongoDb/Models/Products";
import { NextResponse } from "next/server";

// GET ALL USERS
export async function GET(req,{params}) {
    const {id}=await params
    // console.log(id)
    connectToDbAmieShop()
    try {
        const product= await Product.findById(id)
        return NextResponse.json(product,{status:201})
    } catch (error) {
        return NextResponse.json('failed to find product',{status:501})
    }
}

export async function PUT(req,{params}) {
    const {id}=await params
    const body=await req.json()
    console.log(body)
    connectToDbAmieShop()
    try {
        await Product.findByIdAndUpdate(id,body)
        return NextResponse.json('product details updated',{status:201})
    } catch (error) {
        return NextResponse.json('failed to update product details',{status:501})
    }
}

export async function DELETE(request,{params}) {
    const {id}=await params
    console.log(id)
    await connectToDbAmieShop()
    try {
        await Product.findByIdAndDelete(id)
        return NextResponse.json('product deleted', { status: 201})
    } catch (error) {
        console.error(error);
        return NextResponse.json("failed to delete product details", { status: 501 });
    }
}