import { requireAdmin } from '@/middleware/adminAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import ProductManagement from '@/components/admin/ProductManagement';
import { Product } from '@/libs/mongoDb/Models/Products';
import { Brand } from '@/libs/mongoDb/Models/Brand';
import { Category } from '@/libs/mongoDb/Models/Category';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getProductsData(searchParams) {
  await connectToDbAmieShop();
  
  const page = parseInt(searchParams.page) || 1;
  const limit = parseInt(searchParams.limit) || 20;
  const search = searchParams.search || '';
  const brand = searchParams.brand || '';
  const category = searchParams.category || '';
  const status = searchParams.status || '';
  const filter = searchParams.filter || '';

  // Build query
  let query = {};
  
  if (search) {
    query.$or = [
      { title: { $regex: search, $options: 'i' } },
      { brand: { $regex: search, $options: 'i' } },
      { sku: { $regex: search, $options: 'i' } }
    ];
  }
  
  if (brand) query.brand = brand;
  if (category) query.category = { $in: [category] };
  if (status) query.status = status;
  
  // Special filters
  if (filter === 'low-stock') {
    query.stock = { $lte: 10 };
  } else if (filter === 'out-of-stock') {
    query.stock = 0;
  }

  const skip = (page - 1) * limit;

  const [products, total, brands, categories] = await Promise.all([
    Product.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    Product.countDocuments(query),
    Brand.find({ status: 'active' }).sort({ name: 1 }).lean(),
    Category.find({ status: 'active' }).sort({ name: 1 }).lean()
  ]);

  return {
    products: JSON.parse(JSON.stringify(products)),
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    },
    brands: JSON.parse(JSON.stringify(brands)),
    categories: JSON.parse(JSON.stringify(categories))
  };
}

export const metadata = {
  title: 'Product Management - Admin',
  description: 'Manage your product catalog',
};

export default async function AdminProductsPage({ searchParams }) {
  await requireAdmin();
  const data = await getProductsData(searchParams);

  return (
    <AdminLayout>
      <ProductManagement {...data} />
    </AdminLayout>
  );
}
