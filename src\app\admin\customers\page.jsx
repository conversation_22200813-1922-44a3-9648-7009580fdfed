import { requireAdmin } from '@/middleware/adminAuth';
import AdminLayout from '@/components/admin/AdminLayout';
import CustomerManagement from '@/components/admin/CustomerManagement';
import { User } from '@/libs/mongoDb/Models/User';
import { Order } from '@/libs/mongoDb/Models/Order';
import connectToDbAmieShop from '@/libs/mongoDb/connectToDbAmieShop';

async function getCustomersData(searchParams) {
  await connectToDbAmieShop();
  
  const page = parseInt(searchParams.page) || 1;
  const limit = parseInt(searchParams.limit) || 20;
  const search = searchParams.search || '';
  const status = searchParams.status || '';
  const dateFrom = searchParams.dateFrom || '';
  const dateTo = searchParams.dateTo || '';

  // Build query
  let query = { role: { $ne: 'admin' } };
  
  if (search) {
    query.$or = [
      { name: { $regex: search, $options: 'i' } },
      { email: { $regex: search, $options: 'i' } }
    ];
  }
  
  if (status) {
    query.status = status;
  }
  
  if (dateFrom || dateTo) {
    query.createdAt = {};
    if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
    if (dateTo) query.createdAt.$lte = new Date(dateTo + 'T23:59:59.999Z');
  }

  const skip = (page - 1) * limit;

  const [customers, total] = await Promise.all([
    User.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .lean(),
    User.countDocuments(query)
  ]);

  // Get customer order statistics
  const customerIds = customers.map(c => c._id);
  const orderStats = await Order.aggregate([
    { $match: { userId: { $in: customerIds } } },
    {
      $group: {
        _id: '$userId',
        totalOrders: { $sum: 1 },
        totalSpent: { $sum: '$total' },
        lastOrderDate: { $max: '$createdAt' }
      }
    }
  ]);

  const orderStatsMap = orderStats.reduce((acc, stat) => {
    acc[stat._id.toString()] = stat;
    return acc;
  }, {});

  // Enhance customers with order data
  const enhancedCustomers = customers.map(customer => ({
    ...customer,
    orderStats: orderStatsMap[customer._id.toString()] || {
      totalOrders: 0,
      totalSpent: 0,
      lastOrderDate: null
    }
  }));

  return {
    customers: JSON.parse(JSON.stringify(enhancedCustomers)),
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  };
}

export const metadata = {
  title: 'Customer Management - Admin',
  description: 'Manage customer accounts and data',
};

export default async function AdminCustomersPage({ searchParams }) {
  await requireAdmin();
  const data = await getCustomersData(searchParams);

  return (
    <AdminLayout>
      <CustomerManagement {...data} />
    </AdminLayout>
  );
}
