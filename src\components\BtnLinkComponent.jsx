'use client'
import { settings } from '@/libs/siteEcomSettimngs'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import React, { useEffect, useState } from 'react'
import { FaAngleDown } from 'react-icons/fa'

function DynamicLinks({data}) {
  const [item,setItem]=useState()
  const [showMenu,setShowMenu]=useState(false)
  const router=useRouter()
  
  // console.log('DynamicLinks:',data, data?.[Object.keys(data)[0]])
  return(
    <div className='flex relative flex-col w-fit h-fit justify-start items-center object-top'>
      <div onClick={()=>setShowMenu(!showMenu)} className='flex gap-1 items-center capitalize cursor-pointer'>{[Object.keys(data)[0]]}<FaAngleDown className='mt-1' /></div>
      {showMenu && <div className='flex flex-col w-fit h-fit absolute top-5 bg-white shadow-lg items-center justify-start p-2 px-3 left-0 right-0 mx-auto'>
        {data?.[Object.keys(data)[0]].map((option,index)=>
          <Link 
            href={`/${option?.value}`}
            onClick={()=>setShowMenu(!showMenu)} 
            // onClick={(e)=>handleOnchange(e,option?.value)} 
            className={`flex items-center justify-center capitalize text-center text-nowrap cursor-pointer duration-300 ease-linear outline-none ${option?.value==item && 'underline'}`} 
            key={index} value=""
          >
            {option?.value}
          </Link>
        )}
      </div>}
    </div>
  )
}

export default function BtnLinkComponent({data}) {
  const [btnIndex,setbtnIndex]=useState(null)
  const [dynamicLinks,setDynamicLinks]=useState([])
  useEffect(() => {
    setDynamicLinks([
      {brands:data?.brands},
      {collections:data?.collections}
    ])
  }, [data])
  
  const session=true
  const linksLeft=[
    {id:0,name:'home'}
  ]
  const linksRight=[
    {id:10,name:'skin concerns'},
    {id:11,name:'routine'},
    {id:12,name:'accessories'},
    {id:13,name:'sale'},
  ]
  // console.log('LinkBtnComponent:-',dynamicLinks)
  return (
    <>
      {linksLeft.map((link,index)=>
        <Link onClick={()=>setbtnIndex(link.id)} key={link?.id} href={`/${link?.name}`} className={`capitalize active:underline text-lg ${link?.id==btnIndex && 'underline'}`}>
          {link?.name}
        </Link>
      )}
      {dynamicLinks?.length>0 && dynamicLinks?.map((link,index)=>
        <DynamicLinks key={index} data={link}/>
      )}
      {linksRight.map((link,index)=>
        <Link onClick={()=>setbtnIndex(link.id)} key={link?.id} href={`/${link?.name}`} className={`capitalize active:underline text-lg ${link?.id==btnIndex && 'underline'}`}>
          {link?.name}
        </Link>
      )}
      {session && settings.navbar.rightHidden.map((link,index)=>
        <Link onClick={()=>setbtnIndex(link.id)} key={link?.id} href={`/${link?.name}`} className={`capitalize active:underline text-lg ${link?.id==btnIndex && 'underline'}`}>
          {link?.name}
        </Link>
      )}
    </>
  )
}