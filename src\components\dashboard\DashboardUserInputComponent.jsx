'use client'

import { settings } from "@/lib/ecommerce/siteEcomSettimngs"
import { upload } from "@/lib/firebase/uploadFile"
import Image from "next/image"
import { useEffect, useState } from "react"
import { FaRegImage } from "react-icons/fa"

function InputText({data,setInfoToSubmit}) {
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:e.target.value})
    // console.log(e.target.value)
  }
  // console.log('InputText:',)
  return(
    <input onChange={(e)=>handleChange(e)} className='flex px-4 w-full md:h-16 h-10 border-b-gray-200 border-b-[1px] outline-none' {...data}/>
  )
}

function InputRadio({data,setInfoToSubmit}) {
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:true})
    // console.log('InputText:',[data?.name])
  }
  return(
    <fieldset onChange={(e)=>handleChange(e)} className="flex items-center capitalize w-fit gap-5 mt-5  md:h-16 h-10">
      {data?.list.map((item,index)=>
        <div key={index} className="flex w-fit gap-4 items-center">
          <label htmlFor="">{item?.name}</label>
          <input className='flex w-5 h-16 outline-none' {...item}/>
        </div>
      )}
    </fieldset>
  )
}

function InputSelector({data,setInfoToSubmit}) {
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:e.target.value})
  }
  // console.log('InputSelector:',data)
  return(
    <div className="flex w-fit items-center  md:h-16 h-10 gap-4">
      <label className="capitalize" htmlFor="">{data?.name}</label>
      <select  onChange={(e)=>handleChange(e)} className="flex flex-col border-gray-200 border-[1px] gap-2 h-10 rounded px-5 items-center justify-center outline-none" name="" id="">
        {data?.options.map((item,index)=><option key={index} onChange={(e)=>handleChange(e)} className='flex w-full h-full rounded items-center' {...item}>{item?.name}</option>)}
      </select>
    </div>
  )
}

function InputImage({data,setInfoToSubmit,infoToSubmit}) {
  const [err,setErr]=useState('')
  const [file,setFile]=useState()
  const [progress,setProgress]=useState(0)
  const [showError,setShowError]=useState(false)
  const [pending,setPending]=useState(false)

  const handleUpload = () => {
    if(file){
      const fileName=file[0]?.name?.split('.')[0]
      const fileExt=file[0]?.name?.split('.')[file[0]?.name?.split('.').length-1]
      const path=`koreanshop/${infoToSubmit?.username}_${file[0]?.name}`
      file.length>0 && upload(file[0],path,setProgress,setPending,setInfoToSubmit)
      // console.log(file,fileName,fileExt,path)
    }
  }

  useEffect(()=>handleUpload(),[file])

  // console.log('InputText:',file)
  return(
    <div className="flex flex-col w-full items-center h-fit">
      {infoToSubmit?.username.length>3 ? <input onChange={(e)=>setFile(e.target.files)} className='flex w-full justify-center mt-5 items-center ml-[15%] md:h-16 h-10 outline-none' {...data}/> : <span className="flex w-full font-medium">Please enter username to uplaod profile image</span>}
      {/* <button onClick={handleUpload} className="flex bg-gray-900 hover:bg-gray-700 duration-300 ease-linear text-white shadow w-full items-center justify-center uppercase h-10 rounded">upload</button> */}
      <span className="flex w-full items-center justify-center text-center font-medium">{progress}% loaded...</span>
      {progress==100 && <span className="flex w-full items-center justify-center text-center font-medium">upload complete</span>}
    </div>
  )
}

export default function DashboardUserInputComponent() {
  const [infoToSubmit,setInfoToSubmit]=useState({
    username:'',
    email:'',
    firstName:'',
    lastName:'',
    password:'',
    phone:'',
    profileImage:'',
    isAdmin:'',
    role:'',
  })
  const [err,setErr]=useState('')
  const [showError,setShowError]=useState(false)
  const [pending,setPending]=useState(false)

  const inputFeild={
    inputExt:[
      {name:"username",type:"text",placeholder:"username*"},
      {name:"email",type:"email",placeholder:"email*"},
      {name:"password",type:"password",placeholder:"Password*"},
      {name:"firstName",type:"text",placeholder:"First Name"},
      {name:"lastName",type:"text",placeholder:"Last Name"},
      {name:"phone",type:"number",placeholder:"Phone"},
      // "isAdmin",
      // "image",
      // "role",
    ],
    inputFiles:[
      {type:"file",name:"image"},
    ],
    inputselector:[
      "role",
    ],
    inputChecked:[
      {
        name:'is Admin',
        list:[
          {type:"radio",name:"is Admin",value:"isAdmin"}
        ],
      },
    ],
    inputSelector:[
      {
        name:'select user role',
        options:[
          // {type:"options",name:"select user role",value:""},
          {type:"options",name:"client",value:"client"},
          {type:"options",name:"visitor",value:"visitor"}
        ],
      },
    ],
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setPending(true)
    try {
      const res=await fetch(`/api/koreanshop/register`,{method:'POST',body:JSON.stringify(infoToSubmit)})
      if(res.ok){
        setPending(false)
        setShowError(true)
        setErr(await res.json())
      }else{
        setShowError(true)
        setErr(await res.json())
        setPending(false)
      }
    } catch (error) {
      console.log(error)
      setShowError(true)
      setErr(error)
      setPending(false)
    }
  }

  // console.log('DashboardUserInputComponent:',infoToSubmit)
  return (
    <form onSubmit={handleSubmit} className='flex flex-col w-full h-full border-[1px] rounded-lg bg-white shadow-lg gap-2 border-gray-300 items-center justify-center'>
      <div className='flex flex-col md:flex-row w-full h-full gap-2 items-center justify-center'>
        <div className="flex flex-col md:w-1/3 h-[300px] md:h-full w-full md:ml-5 ml-0 md:py-5 py-2 px-2 gap-4">
          <div className="flex relative w-full h-full items-center justify-center bg-gray-100">
            {infoToSubmit.image ? <Image className="object-cover" src={infoToSubmit?.image.profileImage} alt="profile image" fill/> : <FaRegImage className="text-5xl" />}
          </div>
          <div className="flex h-fit w-full">
            {inputFeild.inputFiles.map((item,index)=>
              <InputImage key={index} infoToSubmit={infoToSubmit} setInfoToSubmit={setInfoToSubmit} data={item}/>
            )}
          </div>
        </div>
        <div className='flex flex-col md:w-2/3 h-2/3 md:h-fit md:px-10 px-2' onSubmit={handleSubmit}>
          {inputFeild.inputExt.map((item,index)=>
            <InputText key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
          )}
          <div className="flex flex-col md:flex-row md:h-20 h-fit justify-between md:items-end items-start w-fit md:gap-20">
            {inputFeild.inputChecked.map((item,index)=>
              <InputRadio key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
            )}
            {inputFeild.inputSelector.map((item,index)=>
              <InputSelector key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
            )}
          </div>
          {showError && <span className="flex text-xl font-medium">{err}</span>}
        </div>
      </div>
      <input className="flex w-fit px-28 h-12 bg-gray-900 hover:bg-gray-700 mb-4 rounded-md text-white duration-300 ease-linear" type="submit" />
    </form>
  )
}
