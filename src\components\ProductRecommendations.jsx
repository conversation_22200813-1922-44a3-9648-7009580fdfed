'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import { StarIcon, HeartIcon, ShoppingCartIcon, SparklesIcon } from 'lucide-react';

export default function ProductRecommendations({ 
  type = 'general', // 'general', 'similar', 'frequently-bought', 'skin-profile'
  productId = null,
  skinProfile = null,
  className = ''
}) {
  const { data: session } = useSession();
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRecommendations();
  }, [type, productId, session]);

  const fetchRecommendations = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        type,
        ...(productId && { productId }),
        ...(skinProfile && { skinProfile: JSON.stringify(skinProfile) })
      });

      const response = await fetch(`/api/recommendations?${params}`);
      if (response.ok) {
        const data = await response.json();
        setRecommendations(data.recommendations);
      }
    } catch (error) {
      console.error('Error fetching recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating, count) => {
    return (
      <div className="flex items-center space-x-1">
        <div className="flex">
          {[1, 2, 3, 4, 5].map((star) => (
            <StarIcon
              key={star}
              className={`w-4 h-4 ${
                star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
              }`}
            />
          ))}
        </div>
        {count > 0 && (
          <span className="text-sm text-gray-600">({count})</span>
        )}
      </div>
    );
  };

  const handleAddToCart = async (product) => {
    try {
      const response = await fetch('/api/cart/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          productId: product._id,
          quantity: 1,
          price: product.price,
        }),
      });

      if (response.ok) {
        console.log('Added to cart successfully');
      }
    } catch (error) {
      console.error('Error adding to cart:', error);
    }
  };

  const handleAddToWishlist = async (product) => {
    try {
      const response = await fetch('/api/wishlist/add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ productId: product._id }),
      });

      if (response.ok) {
        console.log('Added to wishlist successfully');
      }
    } catch (error) {
      console.error('Error adding to wishlist:', error);
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'similar':
        return 'Similar Products';
      case 'frequently-bought':
        return 'Frequently Bought Together';
      case 'skin-profile':
        return 'Perfect for Your Skin';
      default:
        return 'Recommended for You';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'skin-profile':
        return <SparklesIcon className="w-5 h-5 text-purple-500" />;
      default:
        return <SparklesIcon className="w-5 h-5 text-blue-500" />;
    }
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-3">
                <div className="aspect-square bg-gray-200 rounded-lg"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (recommendations.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white rounded-lg shadow-sm p-6 ${className}`}>
      <div className="flex items-center space-x-2 mb-6">
        {getIcon()}
        <h3 className="text-lg font-semibold text-gray-900">{getTitle()}</h3>
        {type === 'skin-profile' && (
          <span className="bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded-full">
            AI Powered
          </span>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {recommendations.map((product) => (
          <div
            key={product._id}
            className="group relative bg-white rounded-lg border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300"
          >
            {/* Product Image */}
            <div className="relative aspect-square bg-gray-100">
              <Link href={`/products/${product.slug}`}>
                {product.thumbnail || product.images?.[0] ? (
                  <Image
                    src={product.thumbnail || product.images[0]}
                    alt={product.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-gray-400">
                    No image
                  </div>
                )}
              </Link>

              {/* Sale Badge */}
              {product.onSale && (
                <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs font-semibold rounded">
                  Sale
                </div>
              )}

              {/* Recommendation Score */}
              {product.recommendationScore && (
                <div className="absolute top-2 right-2 bg-blue-500 text-white px-2 py-1 text-xs font-semibold rounded">
                  {Math.round(product.recommendationScore * 100)}% Match
                </div>
              )}

              {/* Hover Actions */}
              <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-2">
                <button
                  onClick={() => handleAddToWishlist(product)}
                  className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors"
                >
                  <HeartIcon className="w-5 h-5 text-gray-700" />
                </button>
                <button
                  onClick={() => handleAddToCart(product)}
                  disabled={!product.inStock}
                  className="bg-white p-2 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ShoppingCartIcon className="w-5 h-5 text-gray-700" />
                </button>
              </div>
            </div>

            {/* Product Info */}
            <div className="p-4 space-y-2">
              <div className="text-sm text-gray-500">{product.brand}</div>
              <h4 className="font-medium text-gray-900 line-clamp-2 group-hover:text-blue-600 transition-colors">
                <Link href={`/products/${product.slug}`}>
                  {product.title}
                </Link>
              </h4>
              
              {/* Rating */}
              {product.ratingCount > 0 && renderStars(product.rating, product.ratingCount)}
              
              {/* Price */}
              <div className="flex items-center space-x-2">
                <span className="font-semibold text-gray-900">
                  P{product.price.toFixed(2)}
                </span>
                {product.salePrice && (
                  <span className="text-sm text-gray-500 line-through">
                    P{product.salePrice.toFixed(2)}
                  </span>
                )}
              </div>

              {/* Skin Match Tags */}
              {product.skinMatch && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {product.skinMatch.slice(0, 2).map((match) => (
                    <span
                      key={match}
                      className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full"
                    >
                      {match}
                    </span>
                  ))}
                </div>
              )}

              {/* Recommendation Reason */}
              {product.recommendationReason && (
                <div className="text-xs text-gray-600 italic">
                  {product.recommendationReason}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* View More */}
      <div className="mt-6 text-center">
        <Link
          href={`/recommendations?type=${type}`}
          className="text-blue-600 hover:text-blue-800 font-medium"
        >
          View More Recommendations →
        </Link>
      </div>
    </div>
  );
}
