'use client'

import { deleteFiles, upload, uploadMulitple } from "@/lib/firebase/uploadFile"
import Image from "next/image"
import { useEffect, useState } from "react"
import { FaRegImage } from "react-icons/fa"
import BtnTrashComponent from "./BtnTrashComponent"
import { usePathname, useRouter } from "next/navigation"

function InputText({data,setInfoToSubmit,style}) {
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:e.target.value})
    // console.log(e.target.value)
  }
  // console.log('InputText:',)
  return(
    data?.type=='text' 
    ? <input onChange={(e)=>handleChange(e)} className={`flex px-4 ${style} md:h-12 h-10 border-b-gray-200 border-b-[1px] outline-none`} {...data}/> :
    data?.type=='number' 
    ? <input onChange={(e)=>handleChange(e)} className={`flex px-4 ${style} md:h-10 h-10 border-b-gray-200 border-b-[1px] outline-none`} {...data}/> :  
      <textarea rows={8} onChange={(e)=>handleChange(e)} className={`flex px-4 ${style} md:h-16 h-10 border-b-gray-200 border-b-[1px] outline-none`} {...data}/>
  )
}

function InputRadio({data,setInfoToSubmit}) {
  const {state,...others}=data
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[others?.name]:true})
    // console.log('InputText:',[data?.name])
  }
  // console.log('InputRadio:',state)
  return(
    <fieldset onChange={(e)=>handleChange(e)} className="flex items-center capitalize w-fit gap-5 mt-5  md:h-16 h-10">
      {others?.list.map((item,index)=>
        <div key={index} className="flex w-fit gap-4 items-center">
          <label htmlFor="">{item?.name}</label>
          <input value={state==='true' && true} className='flex w-5 h-16 outline-none' type="radio"/>
        </div>
      )}
    </fieldset>
  )
}

function InputSelector({data,setInfoToSubmit}) {
  const handleChange = (e) => {
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:e.target.value})
  }
  // console.log('InputSelector:',data)
  return(
    <div className="flex w-fit items-center  md:h-16 h-10 gap-4">
      <label className="capitalize" htmlFor="">{data?.name}</label>
      <select  onChange={(e)=>handleChange(e)} className="flex flex-col border-gray-200 border-[1px] gap-2 h-10 rounded px-5 items-center justify-center outline-none" name="" id="">
        {data?.options.map((item,index)=><option key={index} onChange={(e)=>handleChange(e)} className='flex w-full h-full rounded items-center' {...item}>{item?.name}</option>)}
      </select>
    </div>
  )
}

function InputObject({data,setInfoToSubmit}) {
  const {state,...others}=data
  const [userInfo,setUserInfo]=useState()

  useEffect(()=>{setInfoToSubmit(prev=>prev={...prev,[data?.name]:userInfo})},[userInfo])

  // useEffect(()=>{setlist(state)},[])
  
  // console.log('InputObject:',{userInfo:userInfo})
  return(
    <div className="flex relative flex-col p-1 rounded w-full mt-4 md:mt-0 items-center h-fit gap-4">
      <label className="capitalize underline text-start md:text-center w-full" htmlFor="">{data?.name}</label>
      <div className="flex w-full h-fit flex-wrap">
        {data?.inputs?.map((item,index)=>
          <InputText key={index} style={'w-[18%'} setInfoToSubmit={setUserInfo} data={item}/>
        )}
      </div>
    </div>
  )
}

function InputAddToLIst({data,setInfoToSubmit}) {
  const {state,...others}=data
  const [list,setlist]=useState([])
  const [filteredList,setFilteredList]=useState([])
  const [userInfo,setUserInfo]=useState()
  const [deleteIndex,setDeleteIndex]=useState()

  const handleListFilter= (e,deleteIndex) => {
    e.preventDefault()
    setlist(prev=>prev.filter((item,index)=>item?.id!=deleteIndex?.id))
  }

  const handleClick = (e) => {
    e.preventDefault()
    setlist(prev=>prev=[...prev,{id:prev==undefined ? 0 : prev.length+1,item:userInfo}])
  }

  const updateList = () => {
    list?.length>0 ? setInfoToSubmit(prev=>prev={...prev,[data?.name]:list}) : setlist(prev=>state==undefined ? prev : state)
  }

  useEffect(()=>{updateList()},[list])

  // useEffect(()=>{setlist(state)},[])
  
  // console.log('InputAddToLIst:',{state:state},{list:list})
  return(
    <div className="flex relative flex-col p-1 rounded md:max-w-[45%] w-full mt-4 md:mt-0 items-center h-fit gap-4">
      <label className="capitalize underline text-start md:text-center w-full" htmlFor="">{others?.name}</label>
      <div className="flex w-fit h-fit items-end gap-2 px-4">
        <input onChange={(e)=>setUserInfo(e.target.value)} className='flex px-4 w-full md:h-10 h-10 border-b-gray-200 border-b-[1px] outline-none' {...others}/>
        <button onClick={handleClick} className="flex px-2 h-8 rounded bg-gray-100 ease-linear duration-300 text-xs items-center justify-center hover:bg-gray-300 shadow uppercase">add</button>
      </div>
      <div className="flex w-full flex-wrap min-h-4 overflow-y-auto gap-2 p-2  max-h-40 rounded bg-gray-100">
        {list?.map((item,index)=>
          <div className="flex w-fit gap-2 items-center shadow p-1 px-2 bg-gray-50 rounded h-fit" key={index}>
            <span>{item?.item}</span>
            <div onClick={(e)=>handleListFilter(e,item)} className="flex cursor-pointer items-center justify-center p-2 text-lg text-red-600 shadow rounded w-5 h-5">
              <BtnTrashComponent style={'text-sm'}/>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function InputAddToLIstObject({data,setInfoToSubmit}) {
  const {state,...others}=data
  const [list,setlist]=useState([])
  const [filteredList,setFilteredList]=useState([])
  const [userInfo,setUserInfo]=useState({
    title:'',
    desc:'',
  })
  const [deleteIndex,setDeleteIndex]=useState()

  const handleListFilter= (e,deleteIndex) => {
    e.preventDefault()
    setlist(prev=>prev.filter((item,index)=>item?.id!=deleteIndex?.id))
  }

  const handleClick = (e) => {
    e.preventDefault()
    setlist(prev=>prev=[...prev,{id:prev==undefined ? 0 : prev.length+1,item:userInfo}])
  }

  const updateList = () => {
    list?.length>0 ? setInfoToSubmit(prev=>prev={...prev,[data?.name]:list}) : setlist(prev=>state==undefined ? prev : state)
  }

  useEffect(()=>{updateList()},[list])
  
  // console.log('InputAddToLIst:',{state:state},{userInfo:userInfo})
  // console.log('InputAddToLIst list:',list)
  return(
    <div className="flex select-none relative flex-col p-1 rounded w-full mt-4 md:mt-0 items-center h-fit gap-4">
      <label className="capitalize underline text-start md:text-center w-full" htmlFor="">{others?.name}</label>
      <div className="flex w-fit h-fit items-end gap-2 px-4">
        {data?.map((input,index)=><input key={index} onChange={(e)=>setUserInfo(prev=>prev={...prev,[input?.name]:e.target.value})} className='flex px-4 w-full md:h-10 h-10 border-b-gray-200 border-b-[1px] outline-none' {...input}/>)}
        <button onClick={handleClick} className="flex px-2 h-8 rounded bg-gray-100 ease-linear duration-300 text-xs items-center justify-center hover:bg-gray-300 shadow uppercase">add</button>
      </div>
      <div className="flex w-full flex-wrap min-h-4 overflow-y-auto gap-2 p-2  max-h-40 rounded bg-gray-100">
        {list?.map((item,index)=>
          <div className="flex w-fit gap-2 items-center shadow p-1 px-2 bg-gray-50 rounded h-fit" key={index}>
            <div className="flex w-fit px-2 gap-2">
              <span>{item?.item?.title}</span>:
              <span>{item?.item?.desc}</span>
            </div>
            <div onClick={(e)=>handleListFilter(e,item)} className="flex cursor-pointer items-center justify-center p-2 text-lg text-red-600 shadow rounded w-5 h-5">
              <BtnTrashComponent style={'text-sm'}/>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

function InputFile({data,setInfoToSubmit,infoToSubmit}) {
  const [err,setErr]=useState('')
  const [file,setFile]=useState()
  const [fileArray,setFileArray]=useState([])
  const [progress,setProgress]=useState(0)
  const [showError,setShowError]=useState(false)
  const [pending,setPending]=useState(false)

  const handleUpload = () => {
    if(file){
      // FIREBASE CODE TO UPLOAD
      file.length>0 && uploadMulitple(file,setProgress,setPending,setFileArray,infoToSubmit?.projectTitle,fileArray)

      // 
      console.log(file)
    }
  }
  
  const handleUserInfoUpdate= () => {
    // FIREBASE CODE TO UPLOAD
    // setInfoToSubmit(prev=>{prev,{[data?.name]:fileArray}})
    setInfoToSubmit(prev=>prev={...prev,[data?.name]:fileArray})
    console.log(infoToSubmit)
  }
  
  useEffect(()=>{handleUserInfoUpdate()},[fileArray])

  useEffect(()=>handleUpload(),[file])
  // useEffect(()=>handleUpload(),[fileArray])

  console.log('InputImage:',fileArray,[data?.name])
  return(
    <div className="flex w-[45%] bg-gray-50 rounded h-fit flex-col p-2 shadow-md">
      <span className="capitalize font-medium mb-2">{data?.name}</span>
      <div className="flex flex-col w-fit h-fit items-center">
        {infoToSubmit?.projectTitle?.length>3 ? <input type="file" multiple onChange={(e)=>setFile(e.target.files)} className='flex w-full justify-center items-center ml-[15%] outline-none' name="images"/> : <span className="flex w-full font-medium">Please enter product title to uplaod profile image</span>}
        {/* <button onClick={handleUpload} className="flex bg-gray-900 hover:bg-gray-700 duration-300 ease-linear text-white shadow w-full items-center justify-center uppercase h-10 rounded">upload</button> */}
        {infoToSubmit?.projectTitle?.length>3 && <span className="flex w-full items-center justify-center text-center font-medium">{progress}% loaded...</span>}
        {progress==100 && <span className="flex w-full items-center justify-center text-center font-medium">upload complete</span>}
      </div>
    </div>
  )
}

export default function DashboardProductInputComponent({data}) {

  const [infoToSubmit,setInfoToSubmit]=useState({
    projectTitle:'',
    price:'',
    buildingTitle:'',
    desc:'',
    features:'',
    outroSection:'',
    position:'',
    arPosition:'',
    minDistance:'',
    maxDistance:'',
    warrantyInformation:'',
    shippingInformation:'',
    returnPolicy:'',
    buildingType:'',
    inputBuildingSummary:{
      area:'',
      beds:'',
      level:'',
      baths:'',
      cars:'',
    },
    buildingHighlights:{},
    renders:[],
    drawings:[],
    modelsFiles:[],
    hideLevel:[],
    supportFiles:[],
    _360sImages:[],
    roomSnaps:[],
    presentationDrawings:[],
    constructionDrawingsPdf:[],
    constructionDrawingsDwg:[],
  })

  const [err,setErr]=useState('')
  const [showError,setShowError]=useState(false)
  const [pending,setPending]=useState(false)
  const router=useRouter()
  const pathName=usePathname()

  useEffect(()=>{
    // setInfoToSubmit(data)
    setInfoToSubmit(data)
  },[data])

  const inputFeild={
    inputExt0:[
      {type:"text",required:data?false:true,name:'projectTitle',placeholder:infoToSubmit?.projectTitle ? infoToSubmit?.projectTitle : 'projectTitle'},
      {type:"number",name:'price',placeholder:infoToSubmit?.price ? infoToSubmit?.price : 'price'},
      {type:"text",name:'buildingTitle',placeholder:infoToSubmit?.buildingTitle ? infoToSubmit?.buildingTitle : 'buildingTitle'},
    ],
    inputExt1:[
      {name:'desc',placeholder:infoToSubmit?.desc ? infoToSubmit?.desc : 'desc'},
      {name:'features',placeholder:infoToSubmit?.features ? infoToSubmit?.features : 'features'},
      {name:'outroSection',placeholder:infoToSubmit?.outroSection ? infoToSubmit?.outroSection : 'outroSection'},
    ],
    inputExt2:[
      {type:"text",name:'position',placeholder:infoToSubmit?.position ? infoToSubmit?.position : 'position'},
      {type:"text",name:'arPosition',placeholder:infoToSubmit?.arPosition ? infoToSubmit?.arPosition : 'arPosition'},
      {type:"text",name:'minDistance',placeholder:infoToSubmit?.minDistance ? infoToSubmit?.minDistance : 'minDistance'},
      {type:"text",name:'maxDistance',placeholder:infoToSubmit?.maxDistance ? infoToSubmit?.maxDistance : 'maxDistance'},
    ],
    inputExt3:[
      {type:"text",name:'warrantyInformation',placeholder:infoToSubmit?.warrantyInformation ? infoToSubmit?.warrantyInformation : 'warrantyInformation'},
      {type:"text",name:'shippingInformation',placeholder:infoToSubmit?.shippingInformation ? infoToSubmit?.shippingInformation : 'shippingInformation'},
      {type:"text",name:'returnPolicy',placeholder:infoToSubmit?.returnPolicy ? infoToSubmit?.returnPolicy : 'returnPolicy'},
    ],
    inputBuildingSummary:{
      name:'buildingSummary',
      inputs:[
        {type:"text",name:'area',placeholder:infoToSubmit?.area ? infoToSubmit?.area : 'area'},
        {type:"text",name:'beds',placeholder:infoToSubmit?.beds ? infoToSubmit?.beds : 'beds'},
        {type:"text",name:'level',placeholder:infoToSubmit?.level ? infoToSubmit?.level : 'level'},
        {type:"text",name:'baths',placeholder:infoToSubmit?.baths ? infoToSubmit?.baths : 'baths'},
        {type:"text",name:'cars',placeholder:infoToSubmit?.cars ? infoToSubmit?.cars : 'cars'},
      ]
    },
    buildingHighlights:{
      name:'buildingHighlights',
      input:[
        {type:"text",name:'title',placeholder:infoToSubmit?.title ? infoToSubmit?.title : 'title'},
        {type:"text",name:'desc',placeholder:infoToSubmit?.desc ? infoToSubmit?.desc : 'desc'},
      ]
    },
    inputFiles:[
      {type:'file',name:'renders'},
      {type:'file',name:'drawings'},
      {type:'file',name:'modelsFiles'},
      {type:'file',name:'hideLevel'},
      {type:'file',name:'supportFiles'},
      {type:'file',name:'_360sImages'},
      {type:'file',name:'roomSnaps'},
      {type:'file',name:'presentationDrawings'},
      {type:'file',name:'constructionDrawingsPdf'},
      {type:'file',name:'constructionDrawingsDwg'},
      // {type:"file",name:"image"},
    ],
    inputselector:{
      name:'buildingType',
      options:[
        {name:'buildingType'},
        {name:'buildingType'},
        {name:'buildingType'},
      ]
    },
    inputAddList:[
      {state:infoToSubmit?.tags,type:"text",name:"tags",placeholder:"tags"},
      {state:infoToSubmit?.collections,type:"text",name:"collections",placeholder:"collections"},
      {state:infoToSubmit?.color,type:"text",name:"color",placeholder:"color"},
    ],
    inputChecked:[
      {
        state:infoToSubmit?.inStock,
        name:'inStock',
        list:[
          {type:"radio",name:"in Stock",value:"inStock"}
        ],
      },
    ],
    inputSelector:[
      {
        name:'select user role',
        options:[
          // {type:"options",name:"select user role",value:""},
          {type:"options",name:"client",value:"client"},
          {type:"options",name:"visitor",value:"visitor"}
        ],
      },
    ],
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setPending(true)
    try {
      const res=await fetch(`/api/amieshop/products`,{method:'POST',body:JSON.stringify(infoToSubmit)})
      if(res.ok){
        setPending(false)
        setShowError(true)
        // setErr(await res.json())
        router.push('/amieshop/dashboard/products')
      }else{
        setShowError(true)
        setErr(await res.json())
        setPending(false)
      }
    } catch (error) {
      console.log(error)
      setShowError(true)
      setErr(error)
      setPending(false)
    }
  }

  const handleUpdate = async (e) => {
    e.preventDefault()
    setPending(true)
    try {
      const res=await fetch(`/api/amieshop/products/${data?._id}`,{method:'PUT',body:JSON.stringify(infoToSubmit)})
      if(res.ok){
        setPending(false)
        setShowError(true)
        // setErr(await res.json())
        router.push('/amieshop/dashboard/products')
      }else{
        setShowError(true)
        setErr(await res.json())
        setPending(false)
      }
    } catch (error) {
      console.log(error)
      setShowError(true)
      setErr(error)
      setPending(false)
    }
  }

  const handleDelete = async (e,data) => {
    e.preventDefault()
    // console.log(data?.id)
    // setPending(true)
    // setInfoToSubmit(prev=>{prev?.images.filter((img,index)=>img?.id!==data?.id)})
    // deleteFiles(data?.path)
  }

  console.log('DashboardProductInputComponent:',{infoToSubmit:infoToSubmit},{data:data})
  return (
     <form onSubmit={data ? handleUpdate : handleSubmit} className='flex flex-col w-full h-full border-[1px] rounded-lg bg-white shadow-lg gap-2 border-gray-300 items-center'>
      <div className='flex flex-col md:flex-row w-full h-[calc(100%-72px)] gap-2 items-center justify-center'>
        {/* =========LEFT CONTAINER========= */}
        <div className="expwrapper flex flex-col md:w-1/2 h-[400px] md:h-full w-full md:ml-5 ml-0 md:py-5 py-2 px-2 gap-2">
          {/* =========IMAGES WRAPPER SCROLLER========= */}
          <div className="imagesContainer flex relative w-full h-1/4 items-center bg-gray-100 overflow-x-auto">
            {inputFeild?.inputFiles
              ? <div className="flex flex-col bg-gray-200 w-80 h-full">
                  {inputFeild?.inputFiles?.map((fileArray,index)=>
                    <div key={index} className="flex relative flex-col w-full flex-none h-[200px]">
                      {infoToSubmit?.[fileArray?.name]?.map((file,index)=>
                        <div key={index} className="flex relative flex-col w-64 flex-none h-full">
                          <Image className="object-contain" src={file?.file} alt="profile image" fill/>
                          <div onClick={(e)=>handleDelete(e,file)} className="flex absolute w-5 h-5 rounded p-2 shadow bg-gray-50 items-center justify-center z-10 left-0 right-0 mx-auto bottom-4">
                            <BtnTrashComponent style={'text-sm'}/>
                          </div>
                        </div>
                      )}
                    </div>
                  )} 
                </div> 
              : <div className="flex flex-col items-center justify-center bg-gray-200 w-80 h-full"><FaRegImage className="text-5xl" /></div>}
          </div>
          {/* =========EXPERIENCE FILES WRAPPER========= */}
          <div className="expContainer flex relative w-full h-3/4 items-center justify-center bg-gray-100">
            exp
          </div>
        </div>
        {/* =========LEFT CONTAINER========= */}
        <div className='flex flex-col md:w-1/2 h-2/3 md:h-[calc(100%-20px)] md:px-10 px-2 overflow-y-auto over' onSubmit={handleSubmit}>
          <div className="flex flex-col gap-2 w-full h-fit">
            {/* =========TEXT INPUTS========= */}
            <div className="flex h-fit items-center gap-5 w-full mt-5 flex-wrap">
              {inputFeild.inputExt0.map((item,index)=>
                <InputText style={'w-[30%]'} key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )}
            </div>
            {inputFeild.inputExt1.map((item,index)=>
              <InputText style={'w-full'} key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
            )}
            <div className="flex w-full h-fit flex-wrap gap-4">
              {inputFeild.inputExt2.map((item,index)=>
                <InputText style={'w-[23%]'} key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )}
            </div>
            {inputFeild.inputExt3.map((item,index)=>
              <InputText style={'w-full'} key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
            )}
            {/* =========BUILDING SUMMARY INPUTS========= */}
            <div className="flex w-full h-fit flex-wrap">
              <InputObject style={'w-[18%]'} setInfoToSubmit={setInfoToSubmit} data={inputFeild.inputBuildingSummary}/>
            </div>
            {/* =========ADD TO LIST INPUTS========= */}
            <div className="flex flex-col w-full h-fit flex-wrap">
              <span className="flex capitalize mt-5 font-medium">{inputFeild.buildingHighlights.name}</span>
              <div className="flex w-full h-fit flex-wrap">
                <InputAddToLIstObject style={'w-1/2'} setInfoToSubmit={setInfoToSubmit} data={inputFeild.buildingHighlights.input}/>
              </div>
            </div>
            {/* =========CHECKED INPUTS========= */}
            <div className="flex flex-col md:flex-row md:h-20 h-fit justify-between md:items-end items-start w-fit md:gap-20">
              {inputFeild.inputChecked.map((item,index)=>
                <InputRadio key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )}
            </div>
            {/* =========ADD TO LIST INPUTS========= */}
            <div className="flex h-fit gap-5 w-full flex-wrap">
              {inputFeild.inputAddList.map((item,index)=>
                <InputAddToLIst key={index} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )}
            </div>
            {/* =========FILE INPUTS========= */}
            <div className="flex h-fit gap-5 w-full flex-wrap mt-5">
              {inputFeild.inputFiles.map((item,index)=>
                <InputFile key={index} infoToSubmit={infoToSubmit} setInfoToSubmit={setInfoToSubmit} data={item}/>
              )}
            </div>
          </div>
          {showError && <span className="flex text-xl font-medium">{err}</span>}
        </div>
      </div>
      <input className="flex w-fit px-28 h-12 bg-gray-900 hover:bg-gray-700 mb-4 rounded-md text-white duration-300 ease-linear" type="submit" />
    </form>
  )
}